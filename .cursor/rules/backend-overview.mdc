---
alwaysApply: false
---

## Introduction

The Centaly backend is a modern, type-safe API server built with **Hono.js** that powers the knowledge management platform. It serves as the central data layer and business logic hub, providing secure, scalable APIs for the Next.js dashboard application and future client integrations.

## Architecture Overview

### Technology Stack

- **Runtime**: Node.js 20+ with TypeScript 5.8
- **Framework**: Hono.js - A fast, lightweight web framework
- **Database**: MySQL (PlanetScale) with Prisma ORM
- **Authentication**: Better-Auth for session management
- **Validation**: Zod for runtime type validation
- **Documentation**: OpenAPI/Swagger integration
- **Deployment**: Containerized with Docker on Fly.io

### Core Principles

1. **Type Safety First**: Leverage TypeScript and Zod for end-to-end type safety
2. **API-First Design**: OpenAPI specification drives development with comprehensive documentation
3. **Security by Default**: Built-in authentication, CORS, and security headers
4. **Developer Experience**: Hot reload, structured logging, and comprehensive error handling
5. **Scalability**: Stateless design with connection pooling and caching strategies

## Application Structure

```
apps/backend/src/
├── index.ts              # Application entry point and server setup
├── lib/                  # Core utilities and configuration
│   ├── env.ts           # Environment variable validation
│   └── openapi.ts       # OpenAPI configuration and schemas
├── middleware/          # HTTP middleware functions
│   ├── error-handler.ts # Centralized error handling
│   ├── request-id.ts    # Request tracking
│   └── secure-headers.ts # Security headers
└── routes/              # API route handlers
    ├── index.ts         # Route registration
    ├── users.ts         # User management endpoints
    ├── organizations.ts # Organization endpoints
    └── invitations.ts   # Invitation endpoints
```

#### File Organization and Naming Conventions

- Use **kebab-case** for file names (e.g., `error-handler.ts`, `secure-headers.ts`)
- Use **camelCase** for function and variable names
- Use **PascalCase** for class names (e.g., `APIError`)
- Organize routes in separate files by resource (e.g., `users.ts`, `organizations.ts`)
