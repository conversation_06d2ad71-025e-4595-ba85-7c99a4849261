{"name": "@repo/database", "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.15.4", "exports": {".": "./src/index.ts", "./client": "./src/client.ts"}, "scripts": {"db:generate": "prisma generate", "db:deploy": "prisma migrate deploy", "db:seed": "prisma db seed", "db:studio": "prisma studio", "db:studio-prod": "dotenv -e .env.production -- prisma studio", "db:push": "prisma db push --skip-generate --accept-data-loss", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "dotenv-cli": "^7.4.2"}, "dependencies": {"@planetscale/database": "^1.19.0", "@prisma/adapter-planetscale": "^6.12.0", "@prisma/client": "^6.9.0", "prisma": "^6.9.0"}, "prettier": "@repo/prettier-config"}