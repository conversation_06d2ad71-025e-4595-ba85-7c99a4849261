---
alwaysApply: false
---

---

## alwaysApply: false

## Subdomain Architecture

This application uses a subdomain-based architecture:

Production:

- **Frontend**: `app.centaly.com` (Next.js)
- **Backend API**: `api.centaly.com` (Hono.js on Fly.io)

Staging:

- **Frontend**: `staging.centaly.com` (Next.js)
- **Backend API**: `staging-api.centaly.com` (Hono.js on Fly.io)

Local Development:

- **Frontend**: `http://localhost:3000` (Next.js)
- **Backend API**: `http://localhost:3001` (Hono.js)

## Backend Deployment (Fly.io)

### Backend Environment Variables

Production:

DATABASE_URL=""
COOKIE_DOMAIN=".centaly.com"
FRONTEND_URL="https://app.centaly.com"
BETTER_AUTH_URL="https://api.centaly.com"
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"
BETTER_AUTH_SECRET=""

Staging:

DATABASE_URL=""
COOKIE_DOMAIN=".centaly.com"
FRONTEND_URL="https://staging.centaly.com"
BETTER_AUTH_URL="https://api-staging.centaly.com"
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"
BETTER_AUTH_SECRET=""

### Backend Deployment

We have two backend environments configured on fly.io:

**Production Environment:**

- App: `centaly-backend`
- Region: London (lhr)
- Resources: 8GB RAM, 4 shared CPUs
- Scaling: Min 3 machines, auto-start enabled, no auto-stop
- URL: `https://api.centaly.com`

**Staging Environment:**

- App: `centaly-backend-staging`
- Region: London (lhr)
- Resources: 512MB RAM, 1 shared CPU
- Scaling: Min 1 machine, auto-stop/start enabled
- URL: `https://api-staging.centaly.com`

### Manual Deploy Process

**For Staging:**

```bash
# Deploy to staging
pnpm deploy:staging
# or manually:
fly deploy --config fly.staging.toml
```

**For Production:**

```bash
# Deploy to production (with confirmation prompt)
pnpm deploy:production
# or manually:
fly deploy --config fly.backend.toml
```

### **Verify deployment**:

**Production:**

```bash
# Check app status
fly status --app centaly-backend

# Test health endpoint
curl https://api.centaly.com/health
```

**Staging:**

```bash
# Check app status
fly status --app centaly-backend-staging

# Test health endpoint
curl https://api-staging.centaly.com/health
```

### Dockerfile Structure

The backend uses a multi-stage Dockerfile in the root directory that:

- Handles Turborepo monorepo structure
- Prunes workspace for backend-only dependencies
- Builds all required packages
- Runs with tsx for TypeScript support

### Important Files

- `Dockerfile` (root) - Multi-stage build for monorepo
- `fly.backend.toml` (root) - Production Fly.io configuration
- `fly.staging.toml` (root) - Staging Fly.io configuration
- `.dockerignore` (root) - Excludes unnecessary files
- `scripts/deploy-production.sh` - Production deployment script
- `scripts/deploy-staging.sh` - Staging deployment script

### Useful Commands

**Production:**

```bash
# View app status
fly status --app centaly-backend

# View logs
fly logs --app centaly-backend

# SSH into machine
fly ssh console --app centaly-backend

# Scale resources
fly scale memory 512 --app centaly-backend
fly scale count 2 --app centaly-backend
```

**Staging:**

```bash
# View app status
fly status --app centaly-backend-staging

# View logs
fly logs --app centaly-backend-staging

# SSH into machine
fly ssh console --app centaly-backend-staging

# Scale resources (if needed)
fly scale memory 1024 --app centaly-backend-staging
```

**List secrets for each environment**

```bash

fly secrets list --app centaly-backend
fly secrets list --app centaly-backend-staging

```

## Frontend Deployment (Vercel)

The frontend is deployed to Vercel with the following configuration:

### Environment Variables

Production:

DATABASE_URL=""
COOKIE_DOMAIN=".centaly.com"
FRONTEND_URL="https://app.centaly.com"
BETTER_AUTH_URL="https://api.centaly.com"
NEXT_PUBLIC_API_URL="https://api.centaly.com"
NEXT_PUBLIC_APP_URL="https://app.centaly.com"
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"
BETTER_AUTH_SECRET=""

Staging:

DATABASE_URL=""
COOKIE_DOMAIN=".centaly.com"
FRONTEND_URL="https://staging.centaly.com"
BETTER_AUTH_URL="https://api-staging.centaly.com"
NEXT_PUBLIC_API_URL="https://api-staging.centaly.com"
NEXT_PUBLIC_APP_URL="https://staging.centaly.com"
RESEND_API_KEY=""
EMAIL_FROM="<EMAIL>"
BETTER_AUTH_SECRET=""

### Vercel Project Configuration

The project is configured with:

- **Framework**: Next.js
- **Build Command**: `cd ../.. && turbo run build --filter=dashboard`
- **Install Command**: `cd ../.. && pnpm install`
- **Root Directory**: `apps/dashboard`
- **Node.js Version**: 20.x (specified in package.json engines)

### Build Configuration

The build process includes:

1. **Prisma Client Generation**: The `db:generate` command runs automatically before build (configured in `turbo.json`)
2. **TypeScript Compilation**: Full type checking during build
3. **ESLint Validation**: Code linting with zero warnings tolerance
4. **Static Generation**: Pre-rendered pages where possible

### Domain Configuration

**Production:**

- Domain: `app.centaly.com`
- SSL: Automatically handled by Vercel

**Staging:**

- Domain: Vercel preview URL
- SSL: Automatically handled by Vercel

## CI/CD Pipeline

The project uses GitHub Actions for automated CI/CD with separate workflows for production and staging environments.

### Workflow Overview

#### Production Workflows

**Backend Production (`production-backend.yml`)**

- **Trigger**: Push to `main` branch with backend-related changes
- **Path filters**: `apps/backend/**`, `packages/**` (excluding UI), config files
- **Jobs**:
  1. **Quality Checks**: TypeScript type checking, Prisma client generation, build verification
  2. **Deploy**: Deploy to Fly.io production (`centaly-backend`)
  3. **Database Push**: Conditional schema migration (when commit contains `[schema]` or `[db]`)
- **Environment**: `production`
- **URL**: `https://centaly-backend.fly.dev`

**Frontend Production (`production-dashboard.yml`)**

- **Trigger**: Push to `main` branch with frontend-related changes
- **Path filters**: `apps/dashboard/**`, `packages/**`, config files
- **Jobs**:
  1. **Deploy**: Deploy to Vercel production
- **Environment**: `production`
- **URL**: `https://app.centaly.com`

#### Preview/Staging Workflows

**Backend Preview (`preview-backend.yml`)**

- **Trigger**: Pull requests to `main` branch with backend changes
- **Jobs**:
  1. **Quality Checks**: TypeScript, build verification
  2. **Deploy**: Deploy to Fly.io staging (`centaly-backend-staging`)
- **Environment**: `staging`
- **URL**: `https://centaly-backend-staging.fly.dev`
- **Comments**: Automatically posts deployment status to PR

**Frontend Preview (`preview-dashboard.yml`)**

- **Trigger**: Pull requests to `main` branch with frontend changes
- **Jobs**:
  1. **Deploy Preview**: Deploy to Vercel staging environment
- **Environment**: `preview`
- **URL**: `https://staging.centaly.com`
- **Comments**: Automatically posts preview URL to PR

### Quality Checks

All workflows include comprehensive quality checks:

- **Dependencies**: Frozen lockfile installation (`pnpm install --frozen-lockfile`)
- **TypeScript**: Full type checking (`pnpm typecheck`)
- **Build**: Production build verification
- **Prisma**: Client generation with database connectivity
- **Caching**: Turbo cache optimization for faster builds

### Environment Management

**GitHub Environments:**

- `production`: Protected environment for production deployments
- `staging`: Staging environment for preview deployments
- `preview`: Preview environment for PR deployments

**Required Secrets:**

_Production & Staging:_

```
DATABASE_URL                 # Database connection string
STAGING_DATABASE_URL         # Staging database connection
BETTER_AUTH_SECRET          # Authentication secret key
BETTER_AUTH_URL             # Auth server URL
FRONTEND_URL                # Frontend application URL
RESEND_API_KEY             # Email service API key
EMAIL_FROM                 # From email address
NEXT_PUBLIC_API_URL        # Public API endpoint
NEXT_PUBLIC_APP_URL        # Public app URL
FLY_API_TOKEN              # Fly.io deployment token
VERCEL_TOKEN               # Vercel deployment token
VERCEL_ORG_ID              # Vercel organization ID
VERCEL_PROJECT_ID          # Vercel project ID
```

_Optional (Performance):_

```
TURBO_TOKEN                # Turbo Remote Cache token
TURBO_TEAM                 # Turbo team identifier
```

### Deployment Strategy

**Production Deployment:**

1. **Automatic**: Triggered on push to `main` branch
2. **Path-based**: Only deploys affected applications (backend/frontend)
3. **Sequential**: Quality checks → Deployment → Optional database migration
4. **Verification**: Health checks and deployment status reporting

**Preview Deployment:**

1. **PR-based**: Triggered on pull request creation/updates
2. **Staging Environment**: Uses dedicated staging infrastructure
3. **Comments**: Automatic PR comments with preview URLs
4. **Concurrency**: Cancel previous deployments for same PR

### Database Migrations

**Production:**

- **Conditional**: Only runs when commit message contains `[schema]` or `[db]`
- **Post-deployment**: Runs after successful backend deployment
- **Command**: `pnpm db:push` (Prisma schema push)

**Preview:**

- **Automatic**: Uses staging database for all PR previews
- **Isolated**: Staging database separate from production

### Concurrency Control

- **Production**: `production-backend-${{ github.ref }}` / `production-dashboard-${{ github.ref }}`
- **Preview**: `staging-backend-${{ github.ref }}` / `dashboard-pr-${{ github.event.pull_request.number }}`
- **Behavior**: `cancel-in-progress: true` to prevent resource conflicts

### Monitoring and Notifications

- **Health Checks**: Automated health endpoint verification post-deployment
- **PR Comments**: Deployment status and preview URLs automatically posted
- **Console Logs**: Deployment URLs and status logged to workflow console
- **Environment URLs**: Accessible via GitHub Environments page
