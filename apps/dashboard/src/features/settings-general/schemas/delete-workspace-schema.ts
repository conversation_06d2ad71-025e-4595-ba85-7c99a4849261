import { z } from "zod";

export const deleteWorkspaceSchema = z.object({
  confirmationSlug: z.string().min(1, "Please enter the workspace slug"),
  reason: z.enum(
    [
      "no-longer-needed",
      "switching-providers",
      "cost-concerns",
      "feature-limitations",
      "poor-performance",
      "security-concerns",
      "other"
    ],
    {
      required_error: "Please select a reason for deletion"
    }
  ),
  feedback: z
    .string()
    .max(1000, "Feedback must be less than 1000 characters")
    .optional()
});

export type DeleteWorkspaceFormData = z.infer<typeof deleteWorkspaceSchema>;

export const reasonOptions = [
  { value: "no-longer-needed", label: "No longer needed" },
  { value: "switching-providers", label: "Switching to another provider" },
  { value: "cost-concerns", label: "Cost concerns" },
  { value: "feature-limitations", label: "Feature limitations" },
  { value: "poor-performance", label: "Poor performance" },
  { value: "security-concerns", label: "Security concerns" },
  { value: "other", label: "Other" }
] as const;
