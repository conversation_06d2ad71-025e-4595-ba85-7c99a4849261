---
description:
globs:
alwaysApply: false
---

## Goal

You are tasked with creating a detailed implementation plan in markdown format based on an existing Product Requirements Document (PRD). This implementation plan should outline the high-level steps required to build and deliver the feature.

## Process

1. Receive PRD Reference: The user points to a specific PRD file

2. Analyze PRD: Read and analyse all sections of the specified PRD.

3. Assess Current State: Review the existing codebase to understand existing infrastructure, architectural patterns and conventions. Also, identify any existing components or features that already exist and could be relevant to the PRD requirements. Then, identify existing related files, components, and utilities that can be leveraged or need modification.

4. Generate Tasks: Based on the PRD analysis and current state assessment, generate the key tasks required to implement the feature. Tasks should be in a logical order and broken down into phases where necessary. Structure tasks as a numbered list for clarity (e.g. 1.0)

## Output

Format: Markdown (.md)
Location: /docs/PRD/
Filename: plan-[feature-name].md
