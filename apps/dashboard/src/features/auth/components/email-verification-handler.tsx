"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { authClient } from "@repo/authentication/auth-client";
import { AuthContainer } from "@repo/ui/components/auth-container";
import { Button } from "@repo/ui/components/button";
import { Spinner } from "@repo/ui/components/spinner";

interface EmailVerificationHandlerProps {
  token: string;
}

const EmailVerificationHandler: React.FC<EmailVerificationHandlerProps> = ({
  token
}) => {
  const [error, setError] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Use Better-Auth client method for email verification
        const result = await authClient.verifyEmail({
          query: {
            token: token
          }
        });

        if (result.data) {
          setIsVerified(true);

          // // After successful verification, explicitly check session
          // // This ensures we have the authenticated session before proceeding
          // let session = await authClient.getSession();

          // // If no session immediately available, wait a bit and retry
          // // This handles timing issues in staging/production environments
          // if (!session.data?.session) {
          //   console.log(
          //     "No session found immediately after verification, retrying..."
          //   );
          //   await new Promise((resolve) => setTimeout(resolve, 1000));
          //   session = await authClient.getSession();
          // }

          // if (!session.data?.session) {
          //   // If no session after verification, something went wrong
          //   // Try to refresh the session or handle the error
          //   console.error("No session found after email verification");

          //   // In staging, we might need to manually redirect through sign-in
          //   // Store the intended destination
          //   const pendingInvitationId = sessionStorage.getItem(
          //     "pendingInvitationId"
          //   );
          //   const redirectPath = pendingInvitationId
          //     ? `/onboarding/accept-invitation/${pendingInvitationId}`
          //     : "/onboarding/workspace";

          //   sessionStorage.setItem("postVerificationRedirect", redirectPath);
          //   router.push("/sign-in");
          //   return;
          // }

          // Check if user has a pending invitation
          const pendingInvitationId = sessionStorage.getItem(
            "pendingInvitationId"
          );

          // Only update onboarding status if NOT an invited user
          let onboardingUpdateSuccess = false;
          if (!pendingInvitationId) {
            // Regular sign-up flow - set status to 'workspace'
            try {
              const { updateOnboardingStatus } = await import(
                "@/src/lib/api/onboarding"
              );
              await updateOnboardingStatus("workspace");
              onboardingUpdateSuccess = true;
            } catch (error) {
              console.error(
                "Failed to update onboarding status after email verification:",
                error
              );
              // Continue with redirect even if onboarding status update fails
              // The user's email is verified, which is the primary goal
            }
          }
          // For invited users, we don't update onboarding status here
          // It will be set to 'complete' after they accept the invitation

          // Brief success state before redirect
          setTimeout(
            () => {
              if (pendingInvitationId) {
                // Redirect directly to accept the invitation
                router.push(
                  `/onboarding/accept-invitation/${pendingInvitationId}`
                );
              } else {
                // Redirect directly to workspace onboarding for new users
                router.push("/onboarding/workspace");
              }
            },
            onboardingUpdateSuccess || pendingInvitationId ? 1000 : 2000
          );
        } else if (result.error) {
          throw new Error(result.error.message || "Verification failed");
        }
      } catch (error) {
        console.error("Error verifying email:", error);
        setError("Failed to verify email. The link may be expired or invalid.");
      }
    };

    if (token) {
      verifyEmail();
    } else {
      setError("Invalid verification link.");
    }
  }, [token, router]);

  // Show error state
  if (error) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex flex-col items-center justify-center space-y-4">
          <h2 className="text-3xl">Verification Failed</h2>
          <p className="text-center text-muted-foreground max-w-md">{error}</p>
          <Button
            variant="link"
            onClick={() => router.push("/verify")}
          >
            Back to verification page
          </Button>
        </div>
      </AuthContainer>
    );
  }

  // Show success state briefly before redirect
  if (isVerified) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex justify-center">
          <Spinner />
        </div>
      </AuthContainer>
    );
  }

  // Show loading state while verifying
  return (
    <AuthContainer maxWidth="md">
      <div className="flex justify-center">
        <Spinner />
      </div>
    </AuthContainer>
  );
};

export default EmailVerificationHandler;
