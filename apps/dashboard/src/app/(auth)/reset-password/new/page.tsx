import * as React from "react";
import { redirect } from "next/navigation";
import NewPasswordForm from "@/src/features/auth/components/new-password-form";
import { getAuthenticatedUserRedirect } from "@/src/features/auth/dal";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const dynamic = "force-dynamic";

export default async function NewPasswordPage() {
  // Single API call that handles all auth checks (session, email verification, onboarding, organizations)
  // Returns "/sign-in" only if user is not authenticated (no session or API error)
  // Returns other destinations if user has valid session but needs redirecting
  const redirectTo = await getAuthenticatedUserRedirect();

  if (redirectTo !== "/sign-in") {
    // User has valid session and should be redirected to appropriate destination
    // (workspace, onboarding, verification, etc.)
    redirect(redirectTo);
  }

  return (
    <AuthContainer maxWidth="lg">
      <NewPasswordForm />
    </AuthContainer>
  );
}
