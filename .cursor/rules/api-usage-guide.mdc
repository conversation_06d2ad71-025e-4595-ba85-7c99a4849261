---
alwaysApply: false
---

# API Usage Guide

This guide documents how to use API endpoints in the Centaly codebase, using patterns and conventions established in the project.

## Architecture Overview

The project uses a monorepo structure with:

- **Frontend**: Next.js dashboard (`apps/dashboard/`)
- **Backend**: Hono.js API server (`apps/backend/`) with OpenAPI documentation
- **Shared API utilities**: Common fetch helpers and configuration

## API Configuration

### Base Configuration (`apps/dashboard/src/lib/config/api.ts`)

The API configuration provides environment-aware base URLs and standard HTTP helpers:

```typescript
// Get environment-specific API base URL
export function getApiBaseUrl(): string {
  if (process.env.NODE_ENV === "production") {
    return process.env.NEXT_PUBLIC_API_URL || "https://api.centaly.com";
  }
  return process.env.NEXT_PUBLIC_API_URL || ""; // Uses proxy in development
}

// Core request function with credentials and headers
export async function apiRequest(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response>;

// HTTP method helpers
export async function apiGet(endpoint: string): Promise<Response>;
export async function apiPost(
  endpoint: string,
  data?: unknown
): Promise<Response>;
export async function apiPut(
  endpoint: string,
  data?: unknown
): Promise<Response>;
export async function apiPatch(
  endpoint: string,
  data?: unknown
): Promise<Response>;
export async function apiDelete(endpoint: string): Promise<Response>;
```

**Key Features:**

- Automatic credentials inclusion (`credentials: "include"`)
- JSON content-type headers
- Environment-aware base URL resolution
- Proxy support for development

## Frontend API Usage Patterns

### 1. API Function Structure

Create dedicated API functions in `apps/dashboard/src/lib/api/` directory:

```typescript
// apps/dashboard/src/lib/api/onboarding.ts
import { apiPatch } from "@/src/lib/config/api";

export async function updateOnboardingStatus(
  status: "incomplete" | "workspace" | "invite" | "complete"
) {
  const response = await apiPatch("/api/v1/profile/onboarding-status", {
    onboardingStatus: status,
  });

  if (!response.ok) {
    throw new Error(`Failed to update onboarding status: ${response.status}`);
  }

  return response.json();
}
```

**Pattern Guidelines:**

- Use TypeScript for strict typing
- Import appropriate HTTP helper (`apiGet`, `apiPost`, etc.)
- Include error handling with descriptive messages
- Return parsed JSON response
- Use descriptive function names

### 2. Component Integration

Import and use API functions in components:

```typescript
// apps/dashboard/src/features/auth/components/workspace-onboarding-form.tsx
import {
  updateOnboardingStatus,
  updateDefaultWorkspace,
} from "@/src/lib/api/onboarding";

// Usage in component
const handleWorkspaceCreation = async (workspaceSlug: string) => {
  try {
    await updateDefaultWorkspace(workspaceSlug);
    await updateOnboardingStatus("invite");
    // Handle success
  } catch (error) {
    // Handle error
  }
};
```

### 3. Hook Integration

Use API functions within custom hooks:

```typescript
// apps/dashboard/src/features/auth/hooks/use-workspace.ts
import { apiGet } from "@/src/lib/config/api";

export function useWorkspace() {
  // Hook implementation using apiGet
}
```

## Backend API Structure

### 1. Route Definition Pattern

Backend routes use Hono.js with OpenAPI documentation:

```typescript
// apps/backend/src/routes/users.ts
import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";

// Define route schema
const updateOnboardingStatusRoute = createRoute({
  method: "patch",
  path: "/profile/onboarding-status",
  tags: ["Users"],
  summary: "Update user onboarding status",
  description: "Update the authenticated user's onboarding status",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete",
            ]),
          }),
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete",
            ]),
          }),
        },
      },
      description: "Onboarding status updated",
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error,
        },
      },
      description: "Unauthorized",
    },
  },
});

// Implement route handler
usersRouter.openapi(updateOnboardingStatusRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json({ error: "Unauthorized" }, 401);
  }

  // Implementation logic
});
```

### 2. Router Organization

Routes are organized by domain in separate files:

- `users.ts` - User management endpoints
- `organizations.ts` - Organization management endpoints
- `invitations.ts` - Invitation management endpoints

Each router exports an OpenAPIHono instance:

```typescript
export const usersRouter = new OpenAPIHono<{ Variables: Variables }>();
```

### 3. Route Registration

Routes are registered in `apps/backend/src/routes/index.ts`:

```typescript
export function registerRoutes(app: OpenAPIHono<{ Variables: Variables }>) {
  app.route("/users", usersRouter);
  app.route("/organizations", organizationsRouter);
  app.route("/invitations", invitationsRouter);
}
```

## Common API Endpoints

### User Management

- `GET /api/v1/users` - List users with pagination
- `GET /api/v1/users/{id}` - Get user by ID
- `POST /api/v1/users` - Create a new user
- `GET /api/v1/profile` - Get current user profile (protected)
- `PATCH /api/v1/profile/onboarding-status` - Update onboarding status (protected)
- `PATCH /api/v1/profile/default-workspace` - Update default workspace (protected)
- `GET /api/v1/profile/organizations` - Get user organizations (protected)

### Organization Management

- `GET /api/v1/organizations/{slug}` - Get organization by slug (protected)
- `GET /api/v1/organizations/{slug}/members` - Get organization members (protected)
- `PATCH /api/v1/organizations/{slug}` - Update organization (protected)
- `GET /api/v1/organizations/check-slug/{slug}` - Check slug availability (protected)
- `POST /api/v1/organizations` - Create organization (protected)

### Invitations

- `GET /api/v1/invitations/{id}/validate` - Validate invitation (public)

## Best Practices

### Frontend

1. **Error Handling**: Always include error handling with descriptive messages
2. **Type Safety**: Use TypeScript for request/response types
3. **Organization**: Group related API functions in dedicated files
4. **Naming**: Use descriptive function names that indicate the action
5. **Reusability**: Create reusable API functions rather than inline fetch calls

### Backend

1. **OpenAPI Documentation**: Always document routes with complete schemas
2. **Validation**: Use Zod schemas for request/response validation
3. **Error Responses**: Return consistent error response structures
4. **Authentication**: Check user authentication where required
5. **Organization**: Group related routes in domain-specific files

### General

1. **Consistency**: Follow established patterns for similar functionality
2. **Environment Configuration**: Use environment-aware configuration
3. **Security**: Include proper CORS and authentication middleware
4. **Documentation**: Maintain up-to-date API documentation

## Example: Complete API Integration

### 1. Backend Route

```typescript
// apps/backend/src/routes/users.ts
const updateProfileRoute = createRoute({
  method: "patch",
  path: "/profile",
  // ... OpenAPI schema
});

usersRouter.openapi(updateProfileRoute, async (c) => {
  // Implementation
});
```

### 2. Frontend API Function

```typescript
// apps/dashboard/src/lib/api/profile.ts
export async function updateProfile(data: ProfileData) {
  const response = await apiPatch("/api/v1/profile", data);
  if (!response.ok) {
    throw new Error(`Failed to update profile: ${response.status}`);
  }
  return response.json();
}
```

### 3. Component Usage

```typescript
// apps/dashboard/src/components/profile-form.tsx
import { updateProfile } from "@/src/lib/api/profile";

const handleSubmit = async (data: ProfileData) => {
  try {
    await updateProfile(data);
    // Success handling
  } catch (error) {
    // Error handling
  }
};
```

This pattern ensures type safety, proper error handling, and maintainable API integration across the application.
