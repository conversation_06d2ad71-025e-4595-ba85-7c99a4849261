import { Metada<PERSON> } from "next";
import { redirect } from "next/navigation";
import In<PERSON><PERSON><PERSON><PERSON> from "@/src/features/auth/components/invitation-handler";
import { verifySession } from "@/src/features/auth/dal";

export const metadata: Metadata = {
  title: "Accept Invitation | Centaly"
};

interface PageProps {
  params: Promise<{ id: string }>;
}

export default async function AcceptInvitationPage({ params }: PageProps) {
  // Await the params in Next.js 15
  const { id } = await params;

  // Special case: Can be accessed without authentication
  // But if authenticated, we can provide better UX
  const session = await verifySession();

  // Validate invitation exists (this should be a server-side check)
  const invitationValid = await validateInvitation(id);

  if (!invitationValid) {
    redirect("/");
  }

  return (
    <InvitationHandler
      invitationId={id}
      currentUser={session?.user || null}
    />
  );
}

async function validateInvitation(id: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/invitations/${id}/validate`
    );
    const data = await response.json();
    return data.valid;
  } catch (error) {
    console.error("Failed to validate invitation:", error);
    return false;
  }
}
