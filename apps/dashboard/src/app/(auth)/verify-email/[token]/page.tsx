import * as React from "react";
import { Metadata } from "next";
import { AuthContainer } from "@repo/ui/components/auth-container";

import EmailVerificationHandler from "../../../../features/auth/components/email-verification-handler";

export const metadata: Metadata = {
  title: "Verify Email | Centaly"
};

interface VerifyEmailPageProps {
  params: Promise<{
    token: string;
  }>;
}

export default async function VerifyEmailPage({
  params
}: VerifyEmailPageProps) {
  const { token } = await params;
  return (
    <AuthContainer maxWidth="md">
      <EmailVerificationHandler token={token} />
    </AuthContainer>
  );
}
