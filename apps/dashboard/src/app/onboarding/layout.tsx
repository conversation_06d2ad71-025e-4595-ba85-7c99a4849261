import * as React from "react";
import { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Onboarding | Centaly"
};
export default async function OnboardingLayout({
  children
}: React.PropsWithChildren) {
  return (
    <div className="flex min-h-screen flex-col bg-white">
      <header className="flex h-[60px] w-full items-center bg-white px-4">
        <Link href="/">
          <Image
            priority={true}
            src="/brand/centaly-logo.svg"
            alt="Logo"
            width={140}
            height={40}
            className="cursor-pointer"
          />
        </Link>
      </header>
      <main className="flex grow items-center justify-center">{children}</main>
      <footer className="flex h-[50px] w-full items-center justify-between border-t bg-white px-4">
        <nav>
          <ul className="flex space-x-4">
            <li>
              <a
                href="/terms"
                className="text-xs text-gray-600 hover:text-gray-900"
              >
                Terms & Conditions
              </a>
            </li>
            <li>
              <a
                href="/contact"
                className="text-xs text-gray-600 hover:text-gray-900"
              >
                Contact Us
              </a>
            </li>
            <li>
              <a
                href="/privacy"
                className="text-xs text-gray-600 hover:text-gray-900"
              >
                Privacy Policy
              </a>
            </li>
          </ul>
        </nav>
        <p className="text-xs text-gray-600">
          © 2024 Centaly Ltd. All rights reserved
        </p>
      </footer>
    </div>
  );
}
