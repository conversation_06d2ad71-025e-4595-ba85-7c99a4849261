import { secureHeaders } from "hono/secure-headers";

import { env } from "../lib/env.js";

/**
 * Production-ready secure headers configuration for API
 * Implements defense-in-depth security strategy
 */
export const secureHeadersMiddleware = secureHeaders({
  // Strict Transport Security - Forces HTTPS connections
  strictTransportSecurity: "max-age=31536000; includeSubDomains; preload",

  // Content Security Policy - Prevents XSS and injection attacks
  // Configured for API usage, restrictive by default
  contentSecurityPolicy: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'"],
    styleSrc: ["'self'", "'unsafe-inline'"], // Needed for Swagger UI
    imgSrc: ["'self'", "data:", "https:"],
    connectSrc: ["'self'"],
    fontSrc: ["'self'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'none'"],
    childSrc: ["'none'"],
    workerSrc: ["'none'"],
    manifestSrc: ["'self'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    frameAncestors: ["'none'"],
    upgradeInsecureRequests: []
  },

  // Cross-Origin Policies - Updated for better compatibility
  crossOriginEmbedderPolicy: false, // Disable this for now as it's too restrictive
  crossOriginResourcePolicy: "cross-origin", // API needs to be accessible cross-origin
  crossOriginOpenerPolicy: "same-origin",

  // MIME Type Protection
  xContentTypeOptions: "nosniff",

  // Clickjacking Protection (backup to CSP frame-ancestors)
  xFrameOptions: "DENY",

  // Referrer Policy - Limits information leakage
  referrerPolicy: "strict-origin-when-cross-origin",

  // XSS Protection (legacy but provides defense-in-depth)
  xXssProtection: "1; mode=block"
});

/**
 * Development-friendly secure headers with relaxed CSP for debugging
 */
export const secureHeadersDevMiddleware = secureHeaders({
  // Still enforce HSTS in development for consistency
  strictTransportSecurity: false, // Allow HTTP in development

  // Relaxed CSP for development tools and debugging
  contentSecurityPolicy: {
    defaultSrc: ["'self'"],
    scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'"], // Allow dev tools
    styleSrc: ["'self'", "'unsafe-inline'"],
    imgSrc: ["'self'", "data:", "https:", "http:"],
    connectSrc: ["'self'", "ws:", "wss:", "http:", "https:"], // WebSocket for hot reload
    fontSrc: ["'self'", "data:"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    frameSrc: ["'self'"], // Allow iframes for dev tools
    childSrc: ["'self'"],
    workerSrc: ["'self'"],
    manifestSrc: ["'self'"],
    baseUri: ["'self'"],
    formAction: ["'self'"],
    frameAncestors: ["'self'"]
  },

  // More permissive cross-origin policies for development
  crossOriginEmbedderPolicy: false,
  crossOriginResourcePolicy: "cross-origin",
  crossOriginOpenerPolicy: "unsafe-none",

  // Standard security headers still apply
  xContentTypeOptions: "nosniff",
  xFrameOptions: "SAMEORIGIN", // More permissive for dev tools
  referrerPolicy: "strict-origin-when-cross-origin",
  xXssProtection: "1; mode=block"
});

// Export the appropriate middleware based on environment
export const getSecureHeadersMiddleware = () => {
  return env.NODE_ENV === "production"
    ? secureHeadersMiddleware
    : secureHeadersDevMiddleware;
};
