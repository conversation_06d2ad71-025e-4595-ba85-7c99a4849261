"use client";

import { createContext, ReactNode, useContext } from "react";
import {
  useWorkspace,
  type Workspace
} from "@/src/features/auth/hooks/use-workspace";

interface WorkspaceContextType {
  workspace: Workspace | undefined;
  loading: boolean;
  error: (Error & { status?: number }) | null;
  isError: boolean;
  slug: string;

  // Helper methods
  isAdmin: () => boolean;
  isOwner: () => boolean;
  canManageWorkspace: () => boolean;
  hasPermission: (permission: "read" | "write" | "admin") => boolean;
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(
  undefined
);

interface WorkspaceProviderProps {
  children: ReactNode;
}

export function WorkspaceProvider({ children }: WorkspaceProviderProps) {
  const { workspace, loading, error, isError, slug } = useWorkspace();

  const isAdmin = () => workspace?.userRole === "admin";
  const isOwner = () => workspace?.userRole === "owner";
  const canManageWorkspace = () => isAdmin() || isOwner();

  const hasPermission = (permission: "read" | "write" | "admin") => {
    if (!workspace) return false;

    const { userRole } = workspace;

    switch (permission) {
      case "read":
        return ["member", "admin", "owner"].includes(userRole);
      case "write":
        return ["admin", "owner"].includes(userRole);
      case "admin":
        return userRole === "owner";
      default:
        return false;
    }
  };

  const value: WorkspaceContextType = {
    workspace,
    loading,
    error,
    isError,
    slug,
    isAdmin,
    isOwner,
    canManageWorkspace,
    hasPermission
  };

  return (
    <WorkspaceContext.Provider value={value}>
      {children}
    </WorkspaceContext.Provider>
  );
}

export function useWorkspaceContext() {
  const context = useContext(WorkspaceContext);
  if (context === undefined) {
    throw new Error(
      "useWorkspaceContext must be used within a WorkspaceProvider"
    );
  }
  return context;
}

// Export types for use in other components
export type { WorkspaceContextType };
