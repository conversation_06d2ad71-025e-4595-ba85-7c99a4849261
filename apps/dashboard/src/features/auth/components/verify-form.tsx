"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { authClient, useSession } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";

const VerifyForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const { data: session } = useSession();

  const handleResendEmail = async () => {
    setIsLoading(true);
    setError(null);

    try {
      if (!session?.user?.email) {
        setError("No user session found. Please sign in again.");
        setIsLoading(false);
        return;
      }

      // Call the resend verification endpoint
      await authClient.sendVerificationEmail({
        email: session.user.email
        // Don't set callbackURL here as we're using custom email template
      });

      setSuccess(true);
    } catch (err) {
      console.error("Failed to resend verification email:", err);
      setError("Failed to resend verification email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4 text-center">Verify your email</h1>

        <p className="text-center">
          We&apos;ve sent the confirmation to{" "}
          <span className="font-semibold">
            {session?.user?.email || "your email"}
          </span>
        </p>


        {error && <p className="text-destructive">{error}</p>}

        <div
          aria-label="Email providers"
          className="grid grid-cols-2 gap-3"
        >
          <Button
            variant="outline"
            size="lg"
            className="flex flex-row items-center gap-2 w-full"
            onClick={() => {
              window.open("https://mail.google.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/gmail.svg"
              alt="Gmail"
              width={24}
              height={24}
            />
            <span className="font-semibold">Gmail</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full"
            onClick={() => {
              window.open("https://mail.apple.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/apple-mail.svg"
              alt="Apple Mail"
              width={24}
              height={24}
            />
            <span className="font-semibold">Apple Mail</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full"
            onClick={() => {
              window.open("https://outlook.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/outlook.svg"
              alt="Outlook"
              width={24}
              height={24}
            />
            <span className="font-semibold">Outlook</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-row items-center gap-2 w-full"
            onClick={() => {
              window.open("https://mail.yahoo.com", "_blank");
            }}
          >
            <Image
              priority={true}
              src="/email-providers/yahoo.svg"
              alt="Yahoo"
              width={24}
              height={24}
            />
            <span className="font-semibold">Yahoo!</span>
          </Button>
        </div>

        {success && (
          <p className="text-muted-foreground">
            We resent your verification email to{" "}
            <span className="text-primary">
              {session?.user?.email || "your email"}
            </span>
            . If you don&apos;t see it in your inbox, please check your spam
            folder. If you are still having problems, please{" "}
            <Link
              href="#"
              className="text-primary hover:underline"
            >
              contact support
            </Link>{" "}
            or{" "}
            <button
              className="text-primary hover:underline disabled:opacity-50"
              onClick={handleResendEmail}
              disabled={isLoading}
            >
              {isLoading ? "Resending..." : "try again"}
            </button>
            .
          </p>
        )}

        <div className="space-y-4">
          {!success && (
            <div className="flex items-center justify-center gap-2">
              <p className="text-muted-foreground">
                Didn&apos;t receive the email?
              </p>
              <button
                className="text-primary hover:underline disabled:opacity-50"
                onClick={handleResendEmail}
                disabled={isLoading}
              >
                {isLoading ? "Resending..." : "Resend verification email"}
              </button>
            </div>
          )}
        </div>
      </Card>
    </main>
  );
};

export default VerifyForm;
