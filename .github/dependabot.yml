version: 2
updates:
  # Root package.json
  - package-ecosystem: "pnpm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Dashboard app
  - package-ecosystem: "pnpm"
    directory: "/apps/dashboard"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Backend app
  - package-ecosystem: "pnpm"
    directory: "/apps/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Email app
  - package-ecosystem: "pnpm"
    directory: "/apps/email"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # UI package
  - package-ecosystem: "pnpm"
    directory: "/packages/ui"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Database package
  - package-ecosystem: "pnpm"
    directory: "/packages/database"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Authentication package
  - package-ecosystem: "pnpm"
    directory: "/packages/authentication"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Email package
  - package-ecosystem: "pnpm"
    directory: "/packages/email"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # ESLint config package
  - package-ecosystem: "pnpm"
    directory: "/packages/eslint-config"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # TypeScript config package
  - package-ecosystem: "pnpm"
    directory: "/packages/typescript-config"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # Prettier config package
  - package-ecosystem: "pnpm"
    directory: "/packages/prettier-config"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10

    commit-message:
      prefix: "chore"
      include: "scope"

  # GitHub Actions workflows
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5

    commit-message:
      prefix: "ci"
      include: "scope"
