# Node environment
NODE_ENV=development

# Server configuration
PORT=3001

# MySQL Database
DATABASE_URL=mysql://user:password@localhost:5432/centaly

# Authentication
BETTER_AUTH_SECRET=your-32-character-secret-key-here-minimum-length
BETTER_AUTH_URL=http://localhost:3001

# Production example:
# BETTER_AUTH_URL=https://api.centaly.com

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# Production example:
# FRONTEND_URL=https://app.centaly.com

# Logging
LOG_LEVEL=info

# Email configuration
EMAIL_FROM="<EMAIL>"
RESEND_API_KEY=your-resend-api-key-here