import { Metadata } from "next";
import {
  validateWorkspaceAccess,
  type WorkspacePageProps
} from "@/src/features/auth/contexts/page-validation";

export const metadata: Metadata = {
  title: "Docs | Centaly"
};

export default async function DocsPage({ params }: WorkspacePageProps) {
  const { slug } = await params;

  // Verify user has access to this workspace and get data
  await validateWorkspaceAccess(slug);

  return (
    <div className="flex flex-col p-6">
      <h1>Documentation</h1>
    </div>
  );
}
