import { apiPost, apiPatch, apiDelete } from "@/src/lib/config/api";

export interface PresignedUploadResult {
  uploadUrl: string;
  fileUrl: string;
  key: string;
}

/**
 * Generate a presigned URL for uploading workspace logo
 */
export async function generateUploadUrl(
  organizationSlug: string,
  fileName: string,
  fileType: string,
  fileSize: number
): Promise<PresignedUploadResult> {
  const response = await apiPost(`/api/v1/organizations/${organizationSlug}/upload-logo`, {
    fileName,
    fileType,
    fileSize,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `Failed to generate upload URL: ${response.status}`);
  }

  return response.json();
}

/**
 * Upload file directly to S3 using presigned URL
 */
export async function uploadFileToS3(uploadUrl: string, file: File): Promise<void> {
  const response = await fetch(uploadUrl, {
    method: "PUT",
    body: file,
    headers: {
      "Content-Type": file.type,
    },
  });

  if (!response.ok) {
    let errorMessage = `Failed to upload file to S3: ${response.status} ${response.statusText}`;
    
    try {
      const errorText = await response.text();
      if (errorText) {
        errorMessage += ` - ${errorText}`;
      }
    } catch {
      // If we can't read the error response, just use the status
    }
    
    throw new Error(errorMessage);
  }
}

/**
 * Update workspace logo URL after successful S3 upload
 */
export async function updateWorkspaceLogo(
  organizationSlug: string,
  logoUrl: string
): Promise<void> {
  const response = await apiPatch(`/api/v1/organizations/${organizationSlug}/logo`, {
    logoUrl,
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `Failed to update workspace logo: ${response.status}`);
  }
}

/**
 * Delete workspace logo
 */
export async function deleteWorkspaceLogo(organizationSlug: string): Promise<void> {
  const response = await apiDelete(`/api/v1/organizations/${organizationSlug}/logo`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || `Failed to delete workspace logo: ${response.status}`);
  }
}

/**
 * Complete upload flow: generate URL, upload to S3, update workspace
 */
export async function uploadWorkspaceImage(
  organizationSlug: string,
  file: File
): Promise<string> {
  try {
    // Step 1: Generate presigned URL
    const { uploadUrl, fileUrl } = await generateUploadUrl(
      organizationSlug,
      file.name,
      file.type,
      file.size
    );

    // Step 2: Upload file to S3
    await uploadFileToS3(uploadUrl, file);

    // Step 3: Update workspace logo URL
    await updateWorkspaceLogo(organizationSlug, fileUrl);

    return fileUrl;
  } catch (error) {
    console.error("Upload workflow failed:", error);
    throw error;
  }
}