/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  signInSchema,
  type SignInFormData
} from "@/src/features/auth/schemas/sign-in-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { InputPassword } from "@repo/ui/components/input-password";
import { useForm } from "react-hook-form";

const SignInForm = (): React.JSX.Element => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const router = useRouter();

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<SignInFormData>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: ""
    },
    mode: "onSubmit"
  });

  // Form submit handler using Better-Auth pattern
  const onSubmit = async (data: SignInFormData) => {
    setIsLoading(true);
    setError("");

    // Check for post-verification redirect first
    const postVerificationRedirect = sessionStorage.getItem(
      "postVerificationRedirect"
    );

    // Check if there's a pending invitation
    const pendingInvitationId = sessionStorage.getItem("pendingInvitationId");

    // Determine the callback URL priority:
    // 1. Post-verification redirect (if user came from email verification)
    // 2. Pending invitation redirect
    // 3. Default to home
    const callbackURL = postVerificationRedirect
      ? postVerificationRedirect
      : pendingInvitationId
        ? `/onboarding/accept-invitation/${pendingInvitationId}`
        : "/";

    await authClient.signIn.email(
      {
        email: data.email,
        password: data.password,
        callbackURL
      },

      {
        onRequest: () => {
          setIsLoading(true);
        },
        onSuccess: () => {
          // Clear the post-verification redirect from storage
          if (postVerificationRedirect) {
            sessionStorage.removeItem("postVerificationRedirect");
          }

          // If this was after email verification, use window.location.href for better session propagation
          router.push(callbackURL);
        },
        onError: (ctx: any) => {
          setIsLoading(false);
          // Handle different types of errors
          const errorMessage = ctx.error.message || "Sign in failed";
          setError(errorMessage);
        }
      }
    );
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4">Sign in to Centaly</h1>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your email"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="username"
                      disabled={isLoading}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="password"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="current-password"
                      disabled={isLoading}
                      onChange={(e: any) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex gap-2 justify-between items-center">
              {error && (
                <>
                  <p className="text-red-500 text-xs">{error}</p>
                </>
              )}
              <p className="text-xs text-muted-foreground ml-auto">
                <Link
                  href="/reset-password"
                  className="hover:underline cursor-pointer"
                >
                  Forgot your password?
                </Link>
              </p>
            </div>

            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isLoading}
              onClick={methods.handleSubmit(onSubmit)}
            >
              Sign in
            </Button>
          </form>
        </FormProvider>
        <p>
          Don&apos;t have an account?{" "}
          <Link
            href="/sign-up"
            className="underline"
          >
            Sign up
          </Link>
        </p>
      </Card>
    </main>
  );
};

export default SignInForm;
