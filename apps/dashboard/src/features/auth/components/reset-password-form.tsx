"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { ArrowLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const resetPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address")
});

type FormData = z.infer<typeof resetPasswordSchema>;

const ResetPasswordForm = () => {
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    resolver: zodResolver(resetPasswordSchema)
  });

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setError("");

    try {
      await authClient.forgetPassword({
        email: data.email,
        redirectTo: "/reset-password/new"
      });
      setIsSubmitted(true);
    } catch (error) {
      console.error("Password reset error:", error);
      setError("An error occurred. Please try again.");
    }
  };

  // If the reset link was successfully sent, show a success message
  if (isSubmitted) {
    return (
      <div>
        <div className="flex flex-col space-y-2 p-6 text-center">
          <h1 className="text-3xl pb-4">Check your email</h1>
          <p className="">
            If you have an account, you will receive an email with instructions
            on how to reset your password in a few minutes.
          </p>
        </div>
        <div className="p-6 pt-0 flex flex-col gap-4 text-center">
          <Button
            variant="link"
            className="w-full"
            onClick={() => setIsSubmitted(false)}
          >
            <ArrowLeft className="h-4 w-4" /> Back to reset password
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col space-y-2 p-6">
        <h1 className="text-3xl pb-4">Reset your password</h1>
        <p className="">
          Enter your email address and we will send you instructions to reset
          your password.
        </p>
      </div>
      <div className="p-6 pt-0 flex flex-col gap-4">
        <form
          className="flex flex-col gap-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          {error && <p className="text-sm text-destructive">{error}</p>}
          <div className="space-y-2 flex w-full flex-col">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>
          <Button
            type="submit"
            disabled={isSubmitting}
            size="lg"
            className="w-full"
            loading={isSubmitting}
          >
            Send reset instructions
          </Button>
        </form>
      </div>
      <div className="items-center p-6 pt-0 text-muted-foreground flex justify-start gap-1">
        <p>
          <Link
            className="hover:underline"
            href="/sign-in"
          >
            Back to sign in
          </Link>
        </p>
      </div>
    </div>
  );
};

export default ResetPasswordForm;
