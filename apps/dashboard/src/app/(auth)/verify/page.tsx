import * as React from "react";
import { redirect } from "next/navigation";
import VerifyForm from "@/src/features/auth/components/verify-form";
import { requireAuth } from "@/src/features/auth/dal";
import { AuthContainer } from "@repo/ui/components/auth-container";

export default async function VerifyPage() {
  const session = await requireAuth();

  if (session.user.emailVerified) {
    redirect("/");
  }

  return (
    <AuthContainer maxWidth="md">
      <VerifyForm />
    </AuthContainer>
  );
}
