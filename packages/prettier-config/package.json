{"name": "@repo/prettier-config", "version": "0.0.0", "private": true, "type": "module", "packageManager": "pnpm@9.15.4", "scripts": {"clean": "git clean -xdf .cache .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\"", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@ianvs/prettier-plugin-sort-imports": "4.4.0", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "0.6.9"}, "prettier": "./index.js", "exports": {".": "./index.js"}}