import { cache } from "react";
import { headers } from "next/headers";
import type { 
  Member, 
  MembersResponse, 
  FetchMembersParams 
} from "@/src/lib/api/members";

// Cached server-side data fetching functions
export const getOrganizationMembers = cache(
  async (
    organizationSlug: string, 
    params: FetchMembersParams = {}
  ): Promise<MembersResponse> => {
    try {
      const headersList = await headers();
      
      const searchParams = new URLSearchParams();
      
      if (params.page !== undefined) {
        searchParams.set("page", params.page.toString());
      }
      
      if (params.pageSize !== undefined) {
        searchParams.set("pageSize", params.pageSize.toString());
      }
      
      if (params.status) {
        searchParams.set("status", params.status);
      }
      
      if (params.search) {
        searchParams.set("search", params.search);
      }

      const queryString = searchParams.toString();
      const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationSlug}/members${queryString ? `?${queryString}` : ""}`;
      
      const response = await fetch(url, {
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        cache: "no-store",
        credentials: "include"
      });

      if (!response.ok) {
        console.error(`Failed to fetch members: ${response.status} ${response.statusText}`);
        throw new Error(`Failed to fetch members: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error("Error fetching organization members:", error);
      throw error;
    }
  }
);

export const getMemberById = cache(
  async (
    organizationSlug: string, 
    memberId: string
  ): Promise<Member | null> => {
    try {
      const response = await getOrganizationMembers(organizationSlug);
      return response.members.find(member => member.id === memberId) || null;
    } catch (error) {
      console.error("Error fetching member by ID:", error);
      throw error;
    }
  }
);

// Utility function to get initial data with proper error handling
export async function getInitialMembersData(
  organizationSlug: string,
  params: FetchMembersParams = {}
): Promise<{
  members: Member[];
  pagination: MembersResponse['pagination'];
  error?: string;
}> {
  try {
    const data = await getOrganizationMembers(organizationSlug, params);
    return {
      members: data.members,
      pagination: data.pagination
    };
  } catch (error) {
    console.error("Error loading initial members data:", error);
    return {
      members: [],
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0
      },
      error: error instanceof Error ? error.message : "Failed to load members data"
    };
  }
}