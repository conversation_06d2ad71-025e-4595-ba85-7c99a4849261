/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

export type WelcomeEmailProps = {
  appName: string;
  name: string;
  getStartedLink: string;
};

const HtmlCompat = Html as any;
const ButtonCompat = Button as any;
const HeadCompat = Head as any;
const PreviewCompat = Preview as any;
const BodyCompat = Body as any;
const ContainerCompat = Container as any;
const HeadingCompat = Heading as any;
const TextCompat = Text as any;
const SectionCompat = Section as any;
const HrCompat = Hr as any;

export function WelcomeEmail({
  appName,
  name,
  getStartedLink,
}: WelcomeEmailProps): React.JSX.Element {
  return (
    <HtmlCompat>
      <HeadCompat />
      <PreviewCompat>Welcome to {appName}!</PreviewCompat>
      <Tailwind>
        <BodyCompat className="m-auto bg-white px-2 font-sans">
          <ContainerCompat className="mx-auto my-[40px] max-w-[465px] rounded border border-solid border-[#eaeaea] p-[20px]">
            <HeadingCompat className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
              Welcome to {appName}!
            </HeadingCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">Hello {name},</TextCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              Thank you for signing up! We&apos;re excited to have you on board. Your account has
              been successfully created, and you&apos;re ready to start exploring our platform.
            </TextCompat>
            <SectionCompat className="my-[32px] text-center">
              <ButtonCompat
                href={getStartedLink}
                className="rounded bg-[#000000] px-5 py-3 text-center text-[12px] font-semibold text-white no-underline"
              >
                Get started
              </ButtonCompat>
            </SectionCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              If you have any questions or need assistance, please don&apos;t hesitate to reach out
              to our support team.
            </TextCompat>
            <HrCompat className="mx-0 my-[26px] w-full border border-solid border-[#eaeaea]" />
            <TextCompat className="text-[12px] leading-[24px] text-[#666666]">
              You receive this email because you signed up on {appName}.
            </TextCompat>
          </ContainerCompat>
        </BodyCompat>
      </Tailwind>
    </HtmlCompat>
  );
}
