import { But<PERSON> } from "@repo/ui/components/button";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger
} from "@repo/ui/components/sidebar";
import { Bell, HelpCircle, UserPlus2 } from "lucide-react";

import { DashboardSidebar } from "./dashboard-sidebar";
import UserMenu from "./user-menu";

interface AppShellProps {
  children: React.ReactNode;
}

const DashboardLayoutWrapper = async ({ children }: AppShellProps) => {
  return (
    <SidebarProvider>
      <DashboardSidebar />

      <SidebarInset>
        <header className="flex h-14 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4 w-full justify-between">
            <SidebarTrigger className="-ml-1" />
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="size-7"
              >
                <Bell />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="size-7"
              >
                <HelpCircle />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="size-7"
              >
                <UserPlus2 />
              </Button>

              <UserMenu
                name="John Doe"
                email="<EMAIL>"
                image="https://github.com/shadcn.png"
              />
            </div>
          </div>
        </header>
        {children}
      </SidebarInset>
    </SidebarProvider>
  );
};

export default DashboardLayoutWrapper;
