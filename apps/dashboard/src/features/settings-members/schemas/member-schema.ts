import { z } from "zod";

// Member invitation schema for form validation
export const inviteMemberSchema = z.object({
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  role: z.enum(["member", "admin"], {
    required_error: "Please select a role"
  })
});

export type InviteMemberFormData = z.infer<typeof inviteMemberSchema>;

// Member status filter schema
export const memberStatusFilterSchema = z.object({
  statuses: z.array(z.enum(["pending", "active", "suspended"])).default([]),
  search: z.string().default("")
});

export type MemberStatusFilterData = z.infer<typeof memberStatusFilterSchema>;
