import { Metadata } from "next";
import { validateWorkspaceAccess } from "@/src/features/auth/contexts/page-validation";

export const metadata: Metadata = {
  title: "Search | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function SearchPage({ params }: PageProps) {
  const { slug } = await params;

  // Verify user has access to this workspace and get data
  await validateWorkspaceAccess(slug);

  return (
    <div className="flex flex-col p-6">
      <h1>Search</h1>
    </div>
  );
}
