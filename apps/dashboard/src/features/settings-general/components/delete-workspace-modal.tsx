"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle
} from "@repo/ui/components/dialog";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@repo/ui/components/select";
import { cn } from "@repo/ui/utils/cn";
import { useForm } from "react-hook-form";

import { useDeleteWorkspace } from "../hooks/use-delete-workspace";
import {
  deleteWorkspaceSchema,
  reasonOptions,
  type DeleteWorkspaceFormData
} from "../schemas/delete-workspace-schema";

interface DeleteWorkspaceModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationSlug: string;
}

export function DeleteWorkspaceModal({
  isOpen,
  onClose,
  organizationSlug
}: DeleteWorkspaceModalProps) {
  const deleteWorkspaceMutation = useDeleteWorkspace();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<DeleteWorkspaceFormData>({
    resolver: zodResolver(deleteWorkspaceSchema),
    mode: "onChange"
  });

  const confirmationSlug = watch("confirmationSlug");
  const reason = watch("reason");
  const isSlugValid = confirmationSlug === organizationSlug;
  const isFormValid = isSlugValid && reason;

  const onSubmit = (data: DeleteWorkspaceFormData) => {
    deleteWorkspaceMutation.mutate(
      {
        organizationSlug,
        data
      },
      {
        onSuccess: () => {
          onClose();
        }
      }
    );
  };

  const handleClose = () => {
    if (deleteWorkspaceMutation.isPending) return;
    onClose();
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={handleClose}
    >
      <DialogContent
        className="sm:max-w-md"
        showCloseButton={!deleteWorkspaceMutation.isPending}
      >
        <DialogHeader>
          <DialogTitle className="text-left">Delete Workspace</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm">
            All workspace data will be permanently deleted All team members will
            lose access immediately. This action cannot be undone
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <div>
              <label className="text-xs mb-2 block text-muted-foreground">
                Type{" "}
                <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                  {organizationSlug}
                </span>{" "}
                to confirm:
              </label>
              <Input
                {...register("confirmationSlug")}
                placeholder={organizationSlug}
                disabled={deleteWorkspaceMutation.isPending}
                className={cn(
                  errors.confirmationSlug && "border-red-500",
                  isSlugValid && "border-green-500"
                )}
              />
              {errors.confirmationSlug && (
                <p className="text-xs text-red-500 mt-1">
                  {errors.confirmationSlug.message}
                </p>
              )}
            </div>

            <div>
              <label className="text-xs mb-2 block text-muted-foreground">
                Reason for deleting workspace *
              </label>
              <Select
                value={reason}
                onValueChange={(value) =>
                  setValue("reason", value as DeleteWorkspaceFormData["reason"])
                }
                disabled={deleteWorkspaceMutation.isPending}
              >
                <SelectTrigger
                  className={cn(errors.reason && "border-red-500")}
                >
                  <SelectValue placeholder="Select a reason" />
                </SelectTrigger>
                <SelectContent>
                  {reasonOptions.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.reason && (
                <p className="text-xs text-red-500 mt-1">
                  {errors.reason.message}
                </p>
              )}
            </div>

            <div>
              <label className="text-xs mb-2 block text-muted-foreground">
                Help us improve (optional)
              </label>
              <textarea
                {...register("feedback")}
                placeholder="Tell us how we could have done better..."
                disabled={deleteWorkspaceMutation.isPending}
                className={cn(
                  "flex min-h-20 w-full rounded-sm border border-input bg-background px-3 py-2 text-sm",
                  "placeholder:text-muted-foreground focus-visible:border-ring focus-visible:outline-none",
                  "focus-visible:ring-[3px] focus-visible:ring-ring/20 disabled:cursor-not-allowed disabled:opacity-50",
                  errors.feedback && "border-red-500"
                )}
                rows={3}
              />
              {errors.feedback && (
                <p className="text-xs text-red-500 mt-1">
                  {errors.feedback.message}
                </p>
              )}
            </div>

            <DialogFooter className="gap-2 sm:gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={deleteWorkspaceMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="destructive"
                disabled={!isFormValid || deleteWorkspaceMutation.isPending}
                className="min-w-32"
              >
                {deleteWorkspaceMutation.isPending
                  ? "Deleting..."
                  : "Delete Workspace"}
              </Button>
            </DialogFooter>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
