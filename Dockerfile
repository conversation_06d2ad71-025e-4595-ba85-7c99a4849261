# Base image with Node.js
FROM node:20-alpine AS base

# Install pnpm and turbo globally
RUN npm install -g pnpm@9.15.4 turbo

# Builder stage - prune the workspace for the backend app
FROM base AS builder
WORKDIR /app
# Copy all files from root (monorepo structure)
COPY . .
# Use turbo CLI to prune workspaces for backend
RUN turbo prune backend --docker

# Installer stage - install dependencies
FROM base AS installer
WORKDIR /app

# First install the dependencies (as they change less often)
COPY --from=builder /app/out/json/ .
COPY --from=builder /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
RUN pnpm install --frozen-lockfile

# Copy the full source code
COPY --from=builder /app/out/full/ .

# Generate Prisma client
RUN cd packages/database && pnpm db:generate

# Build all packages and the backend
RUN pnpm build --filter=backend

# Runner stage
FROM base AS runner
WORKDIR /app

# Copy the entire built application with all dependencies
COPY --from=installer /app .

# Set the backend directory as working directory
WORKDIR /app/apps/backend

# Set environment variables
ENV NODE_ENV=production

# Expose the port the backend runs on
EXPOSE 3001

# Run the backend with tsx to handle TypeScript imports
CMD ["pnpm", "exec", "tsx", "src/index.ts"] 