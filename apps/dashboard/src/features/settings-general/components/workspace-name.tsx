"use client";

import React, { useEffect, useState } from "react";
import { useValidatedWorkspace } from "@/src/features/auth/contexts/validated-workspace-context";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { useForm } from "react-hook-form";

import { useUpdateWorkspaceName } from "../hooks/use-update-workspace-name";
import {
  updateWorkspaceNameSchema,
  type UpdateWorkspaceNameFormData
} from "../schemas/workspace-schema";

export default function WorkspaceName() {
  const { currentWorkspace } = useValidatedWorkspace();
  const updateWorkspaceNameMutation = useUpdateWorkspaceName();
  const [showSubmitButton, setShowSubmitButton] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isValid }
  } = useForm<UpdateWorkspaceNameFormData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(updateWorkspaceNameSchema),
    defaultValues: {
      name: currentWorkspace.name
    },
    mode: "onChange"
  });

  const watchedName = watch("name");

  // Show submit button when name differs from current and is valid
  useEffect(() => {
    const hasChanged = watchedName !== currentWorkspace.name;
    setShowSubmitButton(hasChanged && isValid && watchedName.trim() !== "");
  }, [watchedName, currentWorkspace.name, isValid]);

  const onSubmit = async (data: UpdateWorkspaceNameFormData) => {
    try {
      await updateWorkspaceNameMutation.mutateAsync({
        organizationId: currentWorkspace.id,
        name: data.name
      });

      // Reset form to new value to hide submit button
      reset({ name: data.name });
    } catch (error) {
      // Error is handled by the mutation hook
      console.error("Failed to update workspace name:", error);
    }
  };

  const handleCancel = () => {
    reset({ name: currentWorkspace.name });
  };

  return (
    <div className="flex gap-6 w-full border rounded-2xl p-6">
      <div className="flex flex-col gap-1 w-2/5">
        <p className="text-sm font-medium">Workspace Name</p>
        <p className="text-sm text-muted-foreground">
          This is the name that will be displayed throughout Centaly.
        </p>
      </div>

      <div className="flex flex-col gap-4 w-3/5">
        <form
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-4"
        >
          <div>
            <Input
              {...register("name")}
              label="Workspace Name"
              placeholder={currentWorkspace.name}
              disabled={updateWorkspaceNameMutation.isPending}
              error={errors.name?.message}
            />
          </div>

          <div>
            <Input
              label="Workspace Slug"
              placeholder={currentWorkspace.slug || ""}
              disabled
              readOnly
              value={currentWorkspace.slug || ""}
              className="bg-gray-50 text-gray-500"
            />
          </div>

          {showSubmitButton && (
            <div className="flex items-center gap-2 pt-2">
              <Button
                size="sm"
                type="submit"
                loading={updateWorkspaceNameMutation.isPending}
                disabled={!isValid}
              >
                Save
              </Button>
              <Button
                size="sm"
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={updateWorkspaceNameMutation.isPending}
              >
                Cancel
              </Button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}
