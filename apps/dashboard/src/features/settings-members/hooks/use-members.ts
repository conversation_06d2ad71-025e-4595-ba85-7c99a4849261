"use client";

import {
  fetchMembers,
  removeM<PERSON>ber,
  transferOwnership,
  updateMember,
  type FetchMembersParams,
  type Member,
  type MembersResponse,
  type UpdateMemberRequest
} from "@/src/lib/api/members";
import {
  admin,
  authClient,
  useActiveOrganization
} from "@repo/authentication/auth-client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Hook for fetching members with React Query
export function useMembers(
  organizationSlug: string,
  params: FetchMembersParams = {},
  initialData?: MembersResponse
) {
  return useQuery({
    queryKey: ["members", organizationSlug, params],
    queryFn: () => fetchMembers(organizationSlug, params),
    initialData,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
}

// Hook for updating members with optimistic updates
export function useUpdateMember(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      memberId,
      data
    }: {
      memberId: string;
      data: UpdateMemberRequest;
    }) => {
      // Handle ban/unban using Better-Auth admin plugin
      if (data.ban !== undefined) {
        // First get the member to find the userId
        // We need to find the correct query key that matches the cached data
        // Since the query keys include params, we need to search through all cached queries
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        let member: Member | undefined;
        for (const query of memberQueries) {
          const data = query.state.data as MembersResponse | undefined;
          if (data?.members) {
            member = data.members.find((m) => m.id === memberId);
            if (member) break;
          }
        }

        if (!member) {
          throw new Error("Member not found");
        }

        const userId = member.user.id;

        if (data.ban) {
          // Ban user using Better-Auth admin plugin
          const result = await admin.banUser({
            userId: userId,
            banReason: "Account suspended by administrator"
          });

          if (result.error) {
            throw new Error(result.error.message || "Failed to suspend user");
          }
          return result.data;
        } else {
          // Unban user using Better-Auth admin plugin
          const result = await admin.unbanUser({
            userId: userId
          });

          if (result.error) {
            throw new Error(
              result.error.message || "Failed to reactivate user"
            );
          }
          return result.data;
        }
      }

      // Handle other updates (role changes) using existing API
      return updateMember(organizationSlug, memberId, data);
    },
    onMutate: async ({ memberId, data }) => {
      // Cancel any outgoing refetches (using partial match to cancel all member queries)
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value from any existing member query
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.map((member: Member) =>
                  member.id === memberId
                    ? {
                        ...member,
                        ...(data.role && { role: data.role }),
                        // Handle ban logic
                        ...(data.ban !== undefined && {
                          status: data.ban ? "suspended" : "active"
                        })
                      }
                    : member
                )
              };
            }
          );
        }
      }

      // Return a context object with the snapshotted value
      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMembers) {
        // Restore all member queries to their previous state
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to update member");
    },
    onSuccess: (_updatedMember, { data }) => {
      let successMessage = "Member updated successfully";
      if (data.role) {
        successMessage = `Member role updated to ${data.role}`;
      } else if (data.ban !== undefined) {
        successMessage = data.ban ? "Member suspended" : "Member reactivated";
      }
      toast.success(successMessage);
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Hook for removing members with optimistic updates
export function useRemoveMember(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (memberId: string) => removeMember(organizationSlug, memberId),
    onMutate: async (memberId) => {
      // Cancel any outgoing refetches (using partial match to cancel all member queries)
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value from any existing member query
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.filter(
                  (member: Member) => member.id !== memberId
                ),
                pagination: {
                  ...old.pagination,
                  total: old.pagination.total - 1
                }
              };
            }
          );
        }
      }

      // Return a context object with the snapshotted value
      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousMembers) {
        // Restore all member queries to their previous state
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to remove member");
    },
    onSuccess: (_, memberId) => {
      const isInvitation = memberId.startsWith("invitation-");
      toast.success(
        isInvitation ? "Invitation cancelled" : "Member removed successfully"
      );
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Hook to get member status statistics
export function useMemberStats(organizationSlug: string) {
  const { data } = useMembers(organizationSlug);

  return {
    total: data?.pagination.total || 0,
    active: data?.members.filter((m) => m.status === "active").length || 0,
    pending: data?.members.filter((m) => m.status === "pending").length || 0,
    suspended: data?.members.filter((m) => m.status === "suspended").length || 0
  };
}

// Hook for resending invitations
export function useResendInvitation(organizationSlug: string) {
  const queryClient = useQueryClient();
  const { data: activeOrganization } = useActiveOrganization();

  return useMutation({
    mutationFn: async ({
      email,
      role
    }: {
      email: string;
      role: "member" | "admin";
    }) => {
      // Use active organization if available, otherwise find org by slug
      let organizationId = activeOrganization?.id;

      if (!organizationId && organizationSlug) {
        // Fallback: find organization by slug using Better-Auth
        const orgsResult = await authClient.organization.list();
        if (orgsResult.data) {
          const org = orgsResult.data.find((o) => o.slug === organizationSlug);
          if (org) {
            organizationId = org.id;
            // Set this as the active organization for future requests
            await authClient.organization.setActive({ organizationId: org.id });
          }
        }
      }

      if (!organizationId) {
        throw new Error(
          "No organization found. Please refresh the page and try again."
        );
      }

      const result = await authClient.organization.inviteMember({
        email,
        role,
        organizationId,
        resend: true
      });

      if (result.error) {
        throw new Error(result.error.message || "Failed to resend invitation");
      }

      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
      toast.success("Invitation resent successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to resend invitation");
    }
  });
}

// Hook for transferring organization ownership
export function useTransferOwnership(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (newOwnerId: string) => 
      transferOwnership(organizationSlug, newOwnerId),
    onMutate: async (newOwnerId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      // Snapshot the previous value
      const queryCache = queryClient.getQueryCache();
      const memberQueries = queryCache.findAll({
        queryKey: ["members", organizationSlug],
        exact: false
      });

      let previousMembers: MembersResponse | undefined;
      for (const query of memberQueries) {
        const data = query.state.data as MembersResponse | undefined;
        if (data?.members) {
          previousMembers = data;
          break;
        }
      }

      // Optimistically update all member queries
      for (const query of memberQueries) {
        if (query.queryKey) {
          queryClient.setQueryData(
            query.queryKey,
            (old: MembersResponse | undefined) => {
              if (!old?.members) return old;

              return {
                ...old,
                members: old.members.map((member: Member) => {
                  // Update the new owner
                  if (member.id === newOwnerId) {
                    return { ...member, role: "owner" as const };
                  }
                  // Update current owner to admin
                  if (member.role === "owner") {
                    return { ...member, role: "admin" as const };
                  }
                  return member;
                })
              };
            }
          );
        }
      }

      return { previousMembers };
    },
    onError: (error: Error, _variables, context) => {
      // Rollback on error
      if (context?.previousMembers) {
        const queryCache = queryClient.getQueryCache();
        const memberQueries = queryCache.findAll({
          queryKey: ["members", organizationSlug],
          exact: false
        });

        for (const query of memberQueries) {
          if (query.queryKey) {
            queryClient.setQueryData(query.queryKey, context.previousMembers);
          }
        }
      }
      toast.error(error.message || "Failed to transfer ownership");
    },
    onSuccess: (result) => {
      toast.success(`Ownership transferred to ${result.newOwner.user.name || result.newOwner.user.email}`);
    },
    onSettled: () => {
      // Always refetch after completion
      queryClient.invalidateQueries({
        queryKey: ["members", organizationSlug],
        exact: false
      });
    }
  });
}

// Convenience hook for member management with all mutations
export function useMemberManagement(organizationSlug: string) {
  return {
    updateMember: useUpdateMember(organizationSlug),
    removeMember: useRemoveMember(organizationSlug),
    resendInvitation: useResendInvitation(organizationSlug),
    transferOwnership: useTransferOwnership(organizationSlug)
  };
}
