"use client";

import { useTeamMembersFilter } from "@/src/features/settings-members/hooks/use-team-members-filter";
import TeamMembersTable from "@/src/features/settings-members/team-members-table";
import TeamMembersTableFilter from "@/src/features/settings-members/team-members-table-filter";
import type { MembersResponse } from "@/src/features/settings-members/types/member-types";
import { ScrollArea } from "@repo/ui/components/scroll-area";
import { Separator } from "@repo/ui/components/separator";

interface TeamMembersPageClientProps {
  initialData?: MembersResponse;
}

export default function TeamMembersPageClient({ initialData }: TeamMembersPageClientProps) {
  const filterHook = useTeamMembersFilter();

  return (
    <div
      aria-label="General Settings Layout"
      className="relative h-[calc(100vh-3.5rem)]"
    >
      <ScrollArea className="h-full">
        <div className="flex flex-col max-w-[768px] px-6 mx-auto">
          <div className="py-18 space-y-6">
            <div
              aria-label="Header Section"
              className="gap-3"
            >
              <h1 className="text-2xl">Team Members</h1>
              <p className="text-muted-foreground text-sm">
                Manage your workspace team members, and their roles.
              </p>
            </div>
            <Separator className="my-4" />
            <div className="flex flex-col mt-10 gap-4">
              <TeamMembersTableFilter filterHook={filterHook} />
              <TeamMembersTable filterHook={filterHook} initialData={initialData} />
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}
