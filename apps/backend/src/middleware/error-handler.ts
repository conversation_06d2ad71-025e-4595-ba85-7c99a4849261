/* eslint-disable @typescript-eslint/no-explicit-any */
import type { Context, Next } from "hono";
import { HTTPException } from "hono/http-exception";
import { ZodError } from "zod";

import { env } from "../lib/env.js";

export class APIError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public details?: any
  ) {
    super(message);
    this.name = "APIError";
  }
}

export const errorHandler = async (c: Context, next: Next) => {
  try {
    await next();
  } catch (error) {
    // Handle different error types
    if (error instanceof HTTPException) {
      return c.json(
        {
          error: error.message,
          statusCode: error.status,
          timestamp: new Date().toISOString()
        },
        error.status
      );
    }

    if (error instanceof APIError) {
      return c.json(
        {
          error: error.message,
          statusCode: error.statusCode,
          details: error.details,
          timestamp: new Date().toISOString()
        },
        error.statusCode as any
      );
    }

    if (error instanceof ZodError) {
      return c.json(
        {
          error: "Validation error",
          statusCode: 400,
          details: error.format(),
          timestamp: new Date().toISOString()
        },
        400
      );
    }

    // Database errors (Prisma)
    if (error instanceof Error && error.message.includes("P2002")) {
      return c.json(
        {
          error: "Resource already exists",
          statusCode: 409,
          timestamp: new Date().toISOString()
        },
        409
      );
    }

    if (error instanceof Error && error.message.includes("P2025")) {
      return c.json(
        {
          error: "Resource not found",
          statusCode: 404,
          timestamp: new Date().toISOString()
        },
        404
      );
    }

    // Generic error handler
    console.error("Unhandled error:", error);

    const isDev = env.NODE_ENV === "development";
    return c.json(
      {
        error: "Internal server error",
        statusCode: 500,
        ...(isDev && error instanceof Error && { details: error.message }),
        timestamp: new Date().toISOString()
      },
      500
    );
  }
};

// Helper functions for common errors
export const notFound = (message = "Resource not found"): never => {
  throw new APIError(404, message);
};

export const badRequest = (message = "Bad request", details?: any): never => {
  throw new APIError(400, message, details);
};

export const unauthorized = (message = "Unauthorized"): never => {
  throw new APIError(401, message);
};

export const forbidden = (message = "Forbidden"): never => {
  throw new APIError(403, message);
};

export const conflict = (message = "Conflict", details?: any): never => {
  throw new APIError(409, message, details);
};

export const internalError = (
  message = "Internal server error",
  details?: any
): never => {
  throw new APIError(500, message, details);
};
