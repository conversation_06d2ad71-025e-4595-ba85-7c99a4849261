import { Metadata } from "next";
import { validateWorkspaceAccess } from "@/src/features/auth/contexts/page-validation";

export const metadata: Metadata = {
  title: "Home | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function WorkspaceHomePage({ params }: PageProps) {
  const { slug } = await params;

  // Verify user has access to this workspace and get data
  await validateWorkspaceAccess(slug);

  return (
    <div className="flex flex-col p-6">
      <h1>Home</h1>
    </div>
  );
}
