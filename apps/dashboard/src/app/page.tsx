import { redirect } from "next/navigation";
import { getAuthenticatedUserRedirect } from "@/src/features/auth/dal";

export default async function HomePage() {
  // Centralized redirect logic - determines where user should go based on auth status
  // Single API call handles all auth checks (session, verification, onboarding, organizations)
  const redirectTo = await getAuthenticatedUserRedirect();

  // Always redirect to the appropriate destination (sign-in, onboarding, or workspace)
  redirect(redirectTo);
}
