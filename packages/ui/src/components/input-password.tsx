import { useState } from "react";
import { <PERSON>, EyeOff } from "lucide-react";
import { Input } from "./input";
import { cn } from "../utils/cn";

export interface InputPasswordProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: string;
  wrapperClassName?: string;
}

export function InputPassword({
  label,
  helperText,
  error,
  wrapperClassName,
  maxLength,
  autoComplete,
  disabled,
  ...rest
}: InputPasswordProps) {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  const toggleVisibility = () => setIsVisible((prevState) => !prevState);

  return (
    <div className={cn("space-y-1", wrapperClassName)}>
      {label && (
        <label
          htmlFor={rest.id || "password"}
          className="text-[13px] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
        </label>
      )}
      <div className="relative">
        <Input
          id={rest.id || "password"}
          className="pe-9"
          placeholder="Password"
          type={isVisible ? "text" : "password"}
          maxLength={maxLength}
          autoComplete={autoComplete}
          disabled={disabled}
          {...rest} // Forward the remaining props (e.g. value, onChange, ref, etc.)
        />
        <button
          className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg text-muted-foreground/80 outline-offset-2 transition-colors hover:text-foreground focus:z-10 focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50"
          type="button"
          onClick={toggleVisibility}
          aria-label={isVisible ? "Hide password" : "Show password"}
          aria-pressed={isVisible}
          aria-controls={rest.id || "password"}
          disabled={disabled}
        >
          {isVisible ? (
            <EyeOff size={16} strokeWidth={2} aria-hidden="true" />
          ) : (
            <Eye size={16} strokeWidth={2} aria-hidden="true" />
          )}
        </button>
      </div>
      {(helperText || error) && (
        <p
          className={cn(
            "text-xs",
            error ? "text-red-500" : "text-muted-foreground"
          )}
        >
          {error || helperText}
        </p>
      )}
    </div>
  );
}
