"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import type { Todo, CreateTodoData, UpdateTodoData } from "../dal/todos";

// Client-side API functions for mutations
async function createTodoClient(organizationId: string, data: CreateTodoData): Promise<Todo> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
      credentials: "include"
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to create todo: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

async function updateTodoClient(
  organizationId: string,
  todoId: number,
  data: UpdateTodoData
): Promise<Todo> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos/${todoId}`,
    {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
      credentials: "include"
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to update todo: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

async function deleteTodoClient(organizationId: string, todoId: number): Promise<void> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos/${todoId}`,
    {
      method: "DELETE",
      credentials: "include"
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to delete todo: ${response.status} ${response.statusText}`);
  }
}

async function fetchTodos(organizationId: string, options?: {
  page?: number;
  pageSize?: number;
  completed?: boolean;
}) {
  const searchParams = new URLSearchParams();
  if (options?.page) searchParams.set("page", options.page.toString());
  if (options?.pageSize) searchParams.set("pageSize", options.pageSize.toString());
  if (options?.completed !== undefined) searchParams.set("completed", options.completed.toString());

  const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

  const response = await fetch(url, {
    credentials: "include"
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch todos: ${response.status} ${response.statusText}`);
  }

  return response.json();
}

// Hook for fetching todos with React Query
export function useTodos(
  organizationId: string,
  options?: {
    page?: number;
    pageSize?: number;
    completed?: boolean;
  },
  initialData?: { todos: Todo[]; pagination: any }
) {
  return useQuery({
    queryKey: ["todos", organizationId, options],
    queryFn: () => fetchTodos(organizationId, options),
    initialData,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false
  });
}

// Hook for creating todos
export function useCreateTodo(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTodoData) => createTodoClient(organizationId, data),
    onSuccess: () => {
      // Invalidate and refetch todos
      queryClient.invalidateQueries({ queryKey: ["todos", organizationId] });
      toast.success("Todo created successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to create todo");
    }
  });
}

// Hook for updating todos with optimistic updates
export function useUpdateTodo(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ todoId, data }: { todoId: number; data: UpdateTodoData }) =>
      updateTodoClient(organizationId, todoId, data),
    onMutate: async ({ todoId, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["todos", organizationId] });

      // Snapshot the previous value
      const previousTodos = queryClient.getQueryData(["todos", organizationId]);

      // Optimistically update to the new value
      queryClient.setQueryData(["todos", organizationId], (old: { todos: Todo[]; pagination: any } | undefined) => {
        if (!old?.todos) return old;

        return {
          ...old,
          todos: old.todos.map((todo: Todo) =>
            todo.id === todoId
              ? {
                  ...todo,
                  ...data,
                  updatedAt: new Date().toISOString()
                }
              : todo
          )
        };
      });

      // Return a context object with the snapshotted value
      return { previousTodos };
    },
    onError: (error: Error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousTodos) {
        queryClient.setQueryData(["todos", organizationId], context.previousTodos);
      }
      toast.error(error.message || "Failed to update todo");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["todos", organizationId] });
    }
  });
}

// Hook for deleting todos with optimistic updates
export function useDeleteTodo(organizationId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (todoId: number) => deleteTodoClient(organizationId, todoId),
    onMutate: async (todoId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ["todos", organizationId] });

      // Snapshot the previous value
      const previousTodos = queryClient.getQueryData(["todos", organizationId]);

      // Optimistically update to the new value
      queryClient.setQueryData(["todos", organizationId], (old: { todos: Todo[]; pagination: any } | undefined) => {
        if (!old?.todos) return old;

        return {
          ...old,
          todos: old.todos.filter((todo: Todo) => todo.id !== todoId),
          pagination: {
            ...old.pagination,
            total: old.pagination.total - 1
          }
        };
      });

      // Return a context object with the snapshotted value
      return { previousTodos };
    },
    onError: (error: Error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousTodos) {
        queryClient.setQueryData(["todos", organizationId], context.previousTodos);
      }
      toast.error(error.message || "Failed to delete todo");
    },
    onSuccess: () => {
      toast.success("Todo deleted successfully");
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ["todos", organizationId] });
    }
  });
}