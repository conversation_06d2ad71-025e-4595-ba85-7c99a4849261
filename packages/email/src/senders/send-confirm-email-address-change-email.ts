import { render } from "@react-email/render";
import { sendEmail } from "../resend/send-email.js";
import {
  ConfirmEmailAddressChangeEmail,
  ConfirmEmailAddressChangeEmailProps,
} from "../templates/confirm-email-address-change-email.js";

export async function sendConfirmEmailAddressChangeEmail(
  input: ConfirmEmailAddressChangeEmailProps & { recipient: string }
): Promise<void> {
  const component = ConfirmEmailAddressChangeEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Change email instructions",
    html,
    text,
  });
}
