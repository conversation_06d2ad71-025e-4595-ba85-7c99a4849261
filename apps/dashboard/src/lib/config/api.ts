/**
 * API configuration for different environments
 */

// Get API base URL based on environment
export function getApiBaseUrl(): string {
  // In production, use the subdomain approach
  if (process.env.NODE_ENV === "production") {
    return process.env.NEXT_PUBLIC_API_URL || "https://api.centaly.com";
  }
  
  // In development, use the proxy setup
  return process.env.NEXT_PUBLIC_API_URL || "";
}

// Create a configured fetch function
export async function apiRequest(
  endpoint: string,
  options: RequestInit = {}
): Promise<Response> {
  const baseUrl = getApiBaseUrl();
  const url = `${baseUrl}${endpoint}`;
  
  const defaultOptions: RequestInit = {
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  };

  return fetch(url, { ...defaultOptions, ...options });
}

// Helper for GET requests
export async function apiGet(endpoint: string): Promise<Response> {
  return apiRequest(endpoint, { method: "GET" });
}

// Helper for POST requests
export async function apiPost(
  endpoint: string,
  data?: unknown
): Promise<Response> {
  return apiRequest(endpoint, {
    method: "POST",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// Helper for PUT requests
export async function apiPut(
  endpoint: string,
  data?: unknown
): Promise<Response> {
  return apiRequest(endpoint, {
    method: "PUT",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// Helper for PATCH requests
export async function apiPatch(
  endpoint: string,
  data?: unknown
): Promise<Response> {
  return apiRequest(endpoint, {
    method: "PATCH",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// Helper for DELETE requests
export async function apiDelete(endpoint: string): Promise<Response> {
  return apiRequest(endpoint, { method: "DELETE" });
}