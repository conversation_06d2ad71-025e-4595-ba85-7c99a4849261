import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import type { OnboardingStatus } from "../../../../packages/database/generated/prisma/index.js";
import { schemas, type Variables } from "../lib/openapi.js";
import { notFound } from "../middleware/error-handler.js";

// User schema
const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().nullable(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

// Create users router
export const usersRouter = new OpenAPIHono<{ Variables: Variables }>();

// List users
const listUsersRoute = createRoute({
  method: "get",
  path: "/users",
  tags: ["Users"],
  summary: "List all users",
  description: "Get a paginated list of users",
  request: {
    query: z.object({
      page: z.coerce.number().int().positive().default(1).optional(),
      pageSize: z.coerce
        .number()
        .int()
        .positive()
        .max(100)
        .default(10)
        .optional(),
      search: z.string().optional()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            users: z.array(userSchema),
            pagination: schemas.pagination
          })
        }
      },
      description: "List of users"
    }
  }
});

usersRouter.openapi(listUsersRoute, async (c) => {
  const query = c.req.query();
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 10;
  const search = query.search;

  // TODO: Implement actual database query this is a placeholder
  const users = [
    {
      id: "123e4567-e89b-12d3-a456-************",
      email: "<EMAIL>",
      name: "Example User" as string | null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  ];

  return c.json(
    {
      users,
      pagination: {
        page,
        pageSize,
        total: users.length,
        totalPages: Math.ceil(users.length / pageSize)
      }
    },
    200
  );
});

// Get user by ID
const getUserRoute = createRoute({
  method: "get",
  path: "/users/{id}",
  tags: ["Users"],
  summary: "Get user by ID",
  request: {
    params: z.object({
      id: z.string().uuid()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User found"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "User not found"
    }
  }
});

usersRouter.openapi(getUserRoute, async (c) => {
  const { id } = c.req.param();

  // TODO: Implement actual database query
  if (id === "123e4567-e89b-12d3-a456-************") {
    return c.json(
      {
        id,
        email: "<EMAIL>",
        name: "Example User" as string | null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      200
    );
  }

  return notFound("User not found");
});

// Create user
const createUserRoute = createRoute({
  method: "post",
  path: "/users",
  tags: ["Users"],
  summary: "Create a new user",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            email: z.string().email(),
            name: z.string().optional(),
            password: z.string().min(8)
          })
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User created"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Validation error"
    },
    409: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "User already exists"
    }
  }
});

usersRouter.openapi(createUserRoute, async (c) => {
  const body = c.req.valid("json");

  // !: Implement actual user creation
  return c.json(
    {
      id: "123e4567-e89b-12d3-a456-************",
      email: body.email,
      name: body.name || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    },
    201
  );
});

// Get current user profile (protected route)
const getProfileRoute = createRoute({
  method: "get",
  path: "/profile",
  tags: ["Users"],
  summary: "Get current user profile",
  description: "Get the authenticated user's profile information",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: userSchema
        }
      },
      description: "User profile"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(getProfileRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  return c.json(
    {
      id: user.id,
      email: user.email,
      name: user.name || null,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    },
    200
  );
});

// Update onboarding status
const updateOnboardingStatusRoute = createRoute({
  method: "patch",
  path: "/profile/onboarding-status",
  tags: ["Users"],
  summary: "Update user onboarding status",
  description: "Update the authenticated user's onboarding status",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete"
            ])
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            onboardingStatus: z.enum([
              "incomplete",
              "workspace",
              "invite",
              "complete"
            ])
          })
        }
      },
      description: "Onboarding status updated"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(updateOnboardingStatusRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const { onboardingStatus } = c.req.valid("json");

  await prisma.user.update({
    where: { id: user.id },
    data: { onboardingStatus: onboardingStatus as OnboardingStatus }
  });

  return c.json(
    {
      success: true,
      onboardingStatus
    },
    200
  );
});

// Update default workspace
const updateDefaultWorkspaceRoute = createRoute({
  method: "patch",
  path: "/profile/default-workspace",
  tags: ["Users"],
  summary: "Update user default workspace",
  description: "Update the authenticated user's default workspace",
  request: {
    body: {
      content: {
        "application/json": {
          schema: z.object({
            defaultWorkspace: z.string().nullable()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            success: z.boolean(),
            defaultWorkspace: z.string().nullable()
          })
        }
      },
      description: "Default workspace updated"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(updateDefaultWorkspaceRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const { defaultWorkspace } = c.req.valid("json");

  await prisma.user.update({
    where: { id: user.id },
    data: { defaultWorkspace }
  });

  return c.json(
    {
      success: true,
      defaultWorkspace
    },
    200
  );
});

// Get user organizations
const getUserOrganizationsRoute = createRoute({
  method: "get",
  path: "/profile/organizations",
  tags: ["Users"],
  summary: "Get user organizations",
  description: "Get the authenticated user's organizations",
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            organizations: z.array(
              z.object({
                id: z.string(),
                name: z.string(),
                slug: z.string(),
                logo: z.string().nullable(),
                userRole: z.enum(["member", "admin", "owner"]),
                createdAt: z.string()
              })
            )
          })
        }
      },
      description: "User organizations retrieved"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    }
  }
});

usersRouter.openapi(getUserOrganizationsRoute, async (c) => {
  const user = c.get("user");

  if (!user) {
    return c.json(
      {
        error: "Unauthorized",
        timestamp: new Date().toISOString()
      },
      401
    );
  }

  const organizations = await prisma.member.findMany({
    where: { userId: user.id },
    include: {
      organization: {
        select: {
          id: true,
          name: true,
          slug: true,
          logo: true,
          createdAt: true
        }
      }
    },
    orderBy: {
      createdAt: "asc"
    }
  });

  const formattedOrganizations = organizations.map((member) => ({
    id: member.organization.id,
    name: member.organization.name,
    slug: member.organization.slug || "",
    logo: member.organization.logo,
    userRole: member.role as "member" | "admin" | "owner",
    createdAt: member.organization.createdAt.toISOString()
  }));

  return c.json(
    {
      organizations: formattedOrganizations
    },
    200
  );
});
