import { Metadata } from "next";
import { validateWorkspaceAccess } from "@/src/features/auth/contexts/page-validation";

export const metadata: Metadata = {
  title: "Referral Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function ReferralSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  // Verify user has access to this workspace and get data
  await validateWorkspaceAccess(slug);

  return (
    <div className="p-10">
      <h1>Referral Program</h1>
    </div>
  );
}
