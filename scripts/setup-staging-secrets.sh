#!/bin/bash

# Setup environment variables for staging environment
echo "🔐 Setting up secrets for staging environment..."

# Check if app exists
if ! fly apps list | grep -q "centaly-backend-staging"; then
    echo "❌ Error: centaly-backend-staging app not found. Please deploy first."
    exit 1
fi

echo "Setting environment variables for centaly-backend-staging..."

# Authentication
fly secrets set BETTER_AUTH_SECRET=$(openssl rand -base64 32) --app centaly-backend-staging
fly secrets set BETTER_AUTH_URL=https://api-staging.centaly.com --app centaly-backend-staging

# Frontend URL (you might want to change this to your staging frontend URL)
fly secrets set FRONTEND_URL="https://staging.centaly.com" --app centaly-backend-staging

# Email (using staging-specific settings)
fly secrets set EMAIL_FROM="<EMAIL>" --app centaly-backend-staging

# Prompt for environment-specific secrets
echo ""
echo "📝 Please provide the following staging-specific values:"

read -p "Database URL (staging): " DATABASE_URL
if [ -n "$DATABASE_URL" ]; then
    fly secrets set DATABASE_URL="$DATABASE_URL" --app centaly-backend-staging
fi

read -p "Resend API Key (staging): " RESEND_API_KEY
if [ -n "$RESEND_API_KEY" ]; then
    fly secrets set RESEND_API_KEY="$RESEND_API_KEY" --app centaly-backend-staging
fi

echo ""
echo "✅ Staging secrets setup complete!"
echo "🔍 Verify with: fly secrets list --app centaly-backend-staging" 