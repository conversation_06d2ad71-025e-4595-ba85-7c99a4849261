"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { authClient } from "@repo/authentication/auth-client";
import { AuthContainer } from "@repo/ui/components/auth-container";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import { Spinner } from "@repo/ui/components/spinner";

interface InvitationHandlerProps {
  invitationId: string;
  currentUser: {
    id: string;
    email: string;
    name: string | null;
    emailVerified: boolean;
  } | null;
}

interface InvitationDetails {
  organizationName: string;
  organizationSlug: string;
  inviterEmail: string;
  id: string;
  status: "pending" | "accepted" | "rejected" | "canceled";
  email: string;
  expiresAt: Date;
  organizationId: string;
  role: string;
  inviterId: string;
}

const InvitationHandler: React.FC<InvitationHandlerProps> = ({
  invitationId,
  currentUser
}) => {
  const router = useRouter();
  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAcceptLoading, setIsAcceptLoading] = useState(false);
  const [isRejectLoading, setIsRejectLoading] = useState(false);

  useEffect(() => {
    const fetchInvitation = async () => {
      try {
        // For unauthenticated users, redirect to sign-up
        if (!currentUser) {
          // Store invitation ID for after sign-up
          sessionStorage.setItem("pendingInvitationId", invitationId);

          // Fetch invitation details to get email
          const backendUrl =
            process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001";
          const response = await fetch(
            `${backendUrl}/api/v1/invitations/${invitationId}/validate`
          );
          const validationResult = await response.json();

          if (validationResult.email) {
            sessionStorage.setItem(
              "pendingInvitationEmail",
              validationResult.email
            );
          }

          // Redirect to sign-up with invitation context
          router.push(`/sign-up?invitation=${invitationId}`);
          return;
        }

        // For authenticated users, fetch invitation details
        const response = await authClient.organization.getInvitation({
          query: { id: invitationId }
        });

        if (response.error) {
          setError(
            response.error.message || "Failed to fetch invitation details"
          );
          setIsLoading(false);
          return;
        }

        if (response.data.status !== "pending") {
          setError("This invitation is no longer valid");
          setIsLoading(false);
          return;
        }

        // Check if the invitation email matches the logged-in user's email
        if (response.data.email !== currentUser.email) {
          setError("This invitation was sent to a different email address");
          setIsLoading(false);
          return;
        }

        setInvitation(response.data);
        setIsLoading(false);
      } catch (error) {
        console.error("Failed to fetch invitation:", error);
        setError("Failed to fetch invitation details");
        setIsLoading(false);
      }
    };

    fetchInvitation();
  }, [invitationId, currentUser, router]);

  const handleAccept = async () => {
    if (!invitation) return;

    setIsAcceptLoading(true);
    try {
      const response = await authClient.organization.acceptInvitation({
        invitationId
      });

      if (response.data) {
        // Clear any stored invitation ID
        sessionStorage.removeItem("pendingInvitationId");
        sessionStorage.removeItem("pendingInvitationEmail");

        // For newly invited users, update their onboarding status and default workspace
        try {
          // Import the onboarding utilities
          const { updateOnboardingStatus, updateDefaultWorkspace } =
            await import("@/src/lib/api/onboarding");

          // Set onboarding status to complete for invited users
          await updateOnboardingStatus("complete");

          // Set the invited organization as their default workspace
          await updateDefaultWorkspace(invitation.organizationSlug);
        } catch (error) {
          console.error(
            "Failed to update user settings after accepting invitation:",
            error
          );
          // Continue with redirect even if these updates fail
        }

        // Redirect to the workspace
        router.push(`/${invitation.organizationSlug}/home`);
      } else {
        setError(response.error?.message || "Failed to accept invitation");
        setIsAcceptLoading(false);
      }
    } catch (error) {
      console.error("Error accepting invitation:", error);
      setError("An error occurred while accepting the invitation");
      setIsAcceptLoading(false);
    }
  };

  const handleReject = async () => {
    if (!invitation) return;

    setIsRejectLoading(true);
    try {
      const response = await authClient.organization.rejectInvitation({
        invitationId
      });

      if (response.data) {
        // Clear any stored invitation ID
        sessionStorage.removeItem("pendingInvitationId");
        sessionStorage.removeItem("pendingInvitationEmail");

        // Redirect to centaly.com after rejection
        window.location.href = "https://www.centaly.com";
      } else {
        setError(response.error?.message || "Failed to reject invitation");
        setIsRejectLoading(false);
      }
    } catch (error) {
      console.error("Error rejecting invitation:", error);
      setError("An error occurred while rejecting the invitation");
      setIsRejectLoading(false);
    }
  };

  const handleSignOut = async () => {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          router.push("/sign-in");
        }
      }
    });
  };

  // Show loading while checking invitation
  if (isLoading) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex justify-center">
          <Spinner />
        </div>
      </AuthContainer>
    );
  }

  // Show error state
  if (error) {
    return (
      <AuthContainer maxWidth="md">
        <Card className="w-full max-w-lg p-6 space-y-6">
          <div className="flex flex-col items-center gap-6 text-center">
            <h1 className="text-3xl">Invalid invite link</h1>
            <div className="space-y-2 text-start">
              <p>
                There is no invite for{" "}
                <span className="font-semibold">
                  {currentUser?.email || "this email"}
                </span>{" "}
                in this workspace.
              </p>
              <p>
                Invites can only be redeemed using the exact email address they
                were sent to.
              </p>
            </div>
            <ul className="text-left list-disc pl-5 space-y-1">
              <li>
                If you believe you&apos;re using the correct email, please
                contact your admin for help.
              </li>
              <li>
                To try a different email address, sign out and sign back in with
                the correct one.
              </li>
            </ul>
            {currentUser && (
              <Button
                size="lg"
                className="w-full"
                variant="default"
                onClick={handleSignOut}
              >
                Sign Out
              </Button>
            )}
          </div>
        </Card>
      </AuthContainer>
    );
  }

  // Show invitation details
  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div className="flex flex-col space-y-4 justify-center items-center">
          <h1 className="text-3xl text-center pb-4">Organization Invitation</h1>
          <p className="text-center">
            You&apos;ve been invited to join{" "}
            <strong>{invitation?.organizationName}</strong> by{" "}
            <strong>{invitation?.inviterEmail}</strong>
          </p>

          <div className="flex flex-row gap-2">
            <Button
              className="max-w-fit"
              size="lg"
              onClick={handleReject}
              variant="outline"
              loading={isRejectLoading}
              disabled={isAcceptLoading || isRejectLoading}
            >
              Decline
            </Button>
            <Button
              className="max-w-fit"
              size="lg"
              onClick={handleAccept}
              variant="default"
              loading={isAcceptLoading}
              disabled={isAcceptLoading || isRejectLoading}
            >
              Accept Invitation
            </Button>
          </div>
        </div>
      </Card>
    </main>
  );
};

export default InvitationHandler;
