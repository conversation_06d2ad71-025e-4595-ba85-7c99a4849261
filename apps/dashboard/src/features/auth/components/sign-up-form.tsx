/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  signUpSchema,
  type SignUpFormData
} from "@/src/features/auth/schemas/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { InputPassword } from "@repo/ui/components/input-password";
import { useForm } from "react-hook-form";

const SignUpForm = (): React.JSX.Element => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [invitationEmail, setInvitationEmail] = useState<string | null>(null);
  const router = useRouter();

  // Check for pending invitation on mount
  useEffect(() => {
    const pendingInvitationEmail = sessionStorage.getItem(
      "pendingInvitationEmail"
    );

    if (pendingInvitationEmail) {
      setInvitationEmail(pendingInvitationEmail);
    }
  }, []);

  // Initialize form with React Hook Form and Zod validation
  const methods = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: ""
    },
    mode: "onSubmit"
  });

  // Update email field when invitation email is loaded
  useEffect(() => {
    if (invitationEmail) {
      methods.setValue("email", invitationEmail);
    }
  }, [invitationEmail, methods]);

  // Form submit handler using Better-Auth pattern
  const onSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);
    setError("");

    await authClient.signUp.email(
      {
        email: data.email,
        password: data.password,
        name: `${data.firstName} ${data.lastName}`
      },

      {
        onRequest: () => {
          setIsLoading(true);
        },
        onSuccess: async () => {
          // Redirect to verification page
          // Layout-loader will handle invitation flow after email verification
          router.push("/verify");
        },
        onError: (ctx: any) => {
          setIsLoading(false);
          // Handle different types of errors
          // Handle different types of errors - sanitize messages for security
          let errorMessage = "An unexpected error occurred";
          const originalMessage = ctx.error.message || "";

          // Only show safe, user-friendly messages
          if (
            originalMessage.includes("already exists") ||
            originalMessage.includes("duplicate")
          ) {
            errorMessage = "An account with this email already exists";
          } else if (
            originalMessage.includes("invalid") ||
            originalMessage.includes("validation")
          ) {
            errorMessage = "Please check your information and try again";
          } else if (originalMessage.includes("rate limit")) {
            errorMessage = "Too many attempts. Please try again later";
          } else if (originalMessage) {
            errorMessage = originalMessage;
          }
          setError(errorMessage);
        }
      }
    );
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4">Create your account</h1>

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={methods.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="First name"
                        maxLength={50}
                        autoComplete="given-name"
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e);
                          clearErrorsOnChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={methods.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="text"
                        placeholder="Last name"
                        maxLength={50}
                        autoComplete="family-name"
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e);
                          clearErrorsOnChange();
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={methods.control}
              name="email"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      placeholder="Enter your email"
                      maxLength={255}
                      autoCapitalize="off"
                      autoComplete="username"
                      disabled={isLoading || !!invitationEmail}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="password"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <InputPassword
                      {...field}
                      maxLength={72}
                      autoComplete="new-password"
                      disabled={isLoading}
                      onChange={(e: any) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {error && (
              <>
                <p className="text-red-500 text-xs">{error}</p>
              </>
            )}
            <div className="text-[13px] text-neutral-500">
              By clicking continue, you acknowledge that you have read and agree
              to Centalys Terms of Service and Privacy Policy.
            </div>
            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isLoading}
              onClick={methods.handleSubmit(onSubmit)}
            >
              Get started
            </Button>
          </form>
        </FormProvider>
        <p>
          Already have an account?{" "}
          <Link
            href="/sign-in"
            className="underline"
          >
            Sign in
          </Link>
        </p>
      </Card>
    </main>
  );
};

export default SignUpForm;
