"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@repo/ui/components/button";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Input } from "@repo/ui/components/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@repo/ui/components/popover";
import { Search, SlidersHorizontal, UserPlus, X } from "lucide-react";

import type { UseTeamMembersFilterReturn } from "./hooks/use-team-members-filter";
import TeamMemberInviteModal from "./team-member-invite-modal";

const statuses = [
  {
    label: "Active",
    value: "active"
  },
  {
    label: "Pending",
    value: "pending"
  },
  {
    label: "Suspended",
    value: "suspended"
  }
];

interface TeamMembersTableFilterProps {
  filterHook?: UseTeamMembersFilterReturn;
}

export default function TeamMembersTableFilter({
  filterHook
}: TeamMembersTableFilterProps) {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const hasSearchText =
    filterHook?.searchText && filterHook.searchText.length > 0;

  return (
    <div className="flex flex-row justify-start items-center gap-3 w-full">
      <div className="flex flex-row items-center gap-3 w-full">
        <div className="relative w-full">
          <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search members..."
            value={filterHook?.searchText || ""}
            onChange={(e) => filterHook?.setSearchText(e.target.value)}
            className="w-full h-8 pl-9 pr-9"
          />
          {hasSearchText && (
            <Button
              variant="ghost"
              size="iconXs"
              onClick={filterHook?.clearSearch}
              className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
        {/* Filter by status */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
            >
              <SlidersHorizontal
                className="-ms-1 opacity-60 h-4 w-4"
                aria-hidden="true"
              />
              Status
            </Button>
          </PopoverTrigger>
          <PopoverContent
            className="w-auto min-w-36 p-3"
            align="start"
          >
            <div className="space-y-3">
              <div className="text-muted-foreground text-xs font-medium">
                Filters
              </div>
              <div className="space-y-3">
                {statuses.map((status) => (
                  <div
                    key={status.value}
                    className="flex items-center gap-2 text-sm"
                  >
                    <Checkbox
                      checked={
                        filterHook?.selectedStatuses.includes(status.value) ||
                        false
                      }
                      onCheckedChange={() =>
                        filterHook?.toggleStatus(status.value)
                      }
                    />
                    <span className="text-sm">{status.label}</span>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
      <Button
        size="sm"
        onClick={() => setIsInviteModalOpen(true)}
      >
        <UserPlus className="h-4 w-4" />
        Invite member
      </Button>

      {isInviteModalOpen && (
        <TeamMemberInviteModal
          isOpen={isInviteModalOpen}
          onClose={() => setIsInviteModalOpen(false)}
        />
      )}
    </div>
  );
}
