import { render } from "@react-email/render";

import { sendEmail } from "../resend/send-email.js";
import { VerifyEmail, VerifyEmailProps } from "../templates/verify-email.js";

export async function sendVerifyEmail(
  input: VerifyEmailProps & { recipient: string }
): Promise<void> {
  const component = VerifyEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Verify email address",
    html,
    text
  });
}
