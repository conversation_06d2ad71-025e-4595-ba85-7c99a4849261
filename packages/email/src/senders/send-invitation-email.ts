import { render } from "@react-email/render";

import { sendEmail } from "../resend/send-email.js";
import {
  reactInvitationEmail,
  type InviteUserEmailProps
} from "../templates/invite-user-email.js";

export async function sendInvitationEmail(
  input: InviteUserEmailProps & { recipient: string }
): Promise<void> {
  const component = reactInvitationEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Organization invitation",
    html,
    text
  });
}
