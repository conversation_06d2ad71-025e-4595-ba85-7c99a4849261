import { Metadata } from "next";
import { validateWorkspaceAccess } from "@/src/features/auth/contexts/page-validation";
import { getInitialMembersData } from "@/src/features/settings-members/dal/members";

import TeamMembersPageClient from "./team-members-page-client";

export const metadata: Metadata = {
  title: "Team Members Settings | Centaly"
};

interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function TeamMembersSettingsPage({ params }: PageProps) {
  const { slug } = await params;

  // Verify user has access to this workspace and get data
  await validateWorkspaceAccess(slug);

  // Fetch initial members data
  const initialMembersData = await getInitialMembersData(slug);

  return (
    <TeamMembersPageClient 
      initialData={initialMembersData.error ? undefined : {
        members: initialMembersData.members,
        pagination: initialMembersData.pagination
      }} 
    />
  );
}
