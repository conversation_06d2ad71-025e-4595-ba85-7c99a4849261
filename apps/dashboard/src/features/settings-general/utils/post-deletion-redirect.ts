import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { updateDefaultWorkspace } from "@/src/lib/api/onboarding";

interface RedirectionData {
  remainingWorkspaces: Array<{
    slug: string;
    name: string;
  }>;
  wasDefaultWorkspace: boolean;
}

/**
 * <PERSON>les intelligent redirection after workspace deletion
 *
 * Scenarios:
 * 1. No remaining workspaces -> redirect to /sign-up
 * 2. Has remaining workspaces -> set new default and redirect to next workspace
 * 3. Error handling -> fallback to home page
 */
export async function handlePostDeletionRedirect(
  data: RedirectionData,
  router: AppRouterInstance
): Promise<void> {
  try {
    // Scenario 1: No remaining workspaces - redirect to sign-up
    if (data.remainingWorkspaces.length === 0) {
      router.replace("/sign-up");
      return;
    }

    // Scenario 2: Has remaining workspaces
    const nextWorkspace = data.remainingWorkspaces[0]; // First workspace by creation date

    if (!nextWorkspace) {
      // Fallback: if somehow no workspace is available, redirect to sign-up
      router.push("/sign-up");
      return;
    }

    // If the deleted workspace was the default, set a new default
    if (data.wasDefaultWorkspace) {
      try {
        await updateDefaultWorkspace(nextWorkspace.slug);
      } catch (error) {
        console.error("Failed to update default workspace:", error);
        // Continue with redirect even if this fails
      }
    }

    // Redirect to the next available workspace
    router.push(`/${nextWorkspace.slug}/home`);
  } catch (error) {
    console.error("Error in post-deletion redirect:", error);
    // For safety, redirect to sign-up if there's any error
    // This prevents potential redirect loops
    router.replace("/sign-up");
  }
}
