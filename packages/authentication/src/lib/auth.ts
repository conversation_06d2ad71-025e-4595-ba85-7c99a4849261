/* eslint-disable @typescript-eslint/no-explicit-any */
import { prisma } from "@repo/database/client";
import { reactInvitationEmail } from "@repo/email/invite-user-email";
import { resend } from "@repo/email/resend";
import { reactResetPasswordEmail } from "@repo/email/reset-password-email";
import { reactVerifyEmail } from "@repo/email/verify-email";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { admin } from "better-auth/plugins/admin";
import { customSession } from "better-auth/plugins/custom-session";
import { organization } from "better-auth/plugins/organization";
import { config } from "dotenv";

config({ path: "../../apps/backend/.env" });
config({ path: ".env" });

const from = process.env.EMAIL_FROM;

if (!from) {
  throw new Error(
    "EMAIL_FROM environment variable is required. Please set it in your .env file."
  );
}

export const auth: any = betterAuth({
  appName: "Centaly",
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3001",
  database: prismaAdapter(prisma, {
    provider: "mysql"
  }),

  // Database hooks to automatically set active organization on session creation
  databaseHooks: {
    session: {
      create: {
        before: async (session) => {
          // Find the user's first organization (by membership creation date)
          const userMember = await prisma.member.findFirst({
            where: { userId: session.userId },
            orderBy: { createdAt: "asc" }, // Get their oldest membership (first joined)
            include: {
              organization: true
            }
          });

          // If user is a member of any organization, set it as active
          if (userMember) {
            return {
              data: {
                ...session,
                activeOrganizationId: userMember.organizationId
              }
            };
          }

          // If no organization membership, keep activeOrganizationId as null
          return { data: session };
        }
      }
    }
  },

  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
    updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)
    cookieCache: {
      enabled: true,
      maxAge: 5 * 60 // Cache duration in seconds
    }
  },

  // Configure cookies for cross-origin setup
  advanced: {
    crossSubDomainCookies: {
      enabled: process.env.NODE_ENV === "production",
      domain: ".centaly.com"
    },

    cookies: {
      session_token: {
        name: "better-auth.session_token",
        attributes: {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          sameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
          path: "/"
        }
      }
    }
  },

  trustedOrigins: [
    "http://localhost:3000",
    "http://localhost:3001",
    "https://staging.centaly.com",
    "https://app.centaly.com",
    "https://api-staging.centaly.com",
    "https://api.centaly.com",
    process.env.FRONTEND_URL || "http://localhost:3000",
    // Allow Vercel preview deployments
    ...(process.env.VERCEL_URL ? [`https://${process.env.VERCEL_URL}`] : [])
  ].filter(Boolean),

  rateLimit: {
    window: 10, // time window in seconds
    max: 10 // max requests in the window
  },

  emailAndPassword: {
    autoSignIn: true,
    enabled: true,
    async sendResetPassword({ user, url, token }) {
      // Extract token from the URL or use the token parameter
      const resetToken =
        token || url.split("/api/auth/reset-password/")[1]?.split("?")[0];

      // Create our custom reset URL that points to our frontend page
      const customResetLink =
        process.env.NODE_ENV === "development"
          ? `http://localhost:3000/reset-password/new?token=${resetToken}`
          : `${process.env.FRONTEND_URL || "https://app.centaly.com"}/reset-password/new?token=${resetToken}`;

      await resend.emails.send({
        from,
        to: user.email,
        subject: "Reset your password",
        react: reactResetPasswordEmail({
          name: user.name,
          resetLink: customResetLink
        })
      });
    }
  },

  emailVerification: {
    sendOnSignUp: true,
    autoSignInAfterVerification: true,
    async sendVerificationEmail({ user, url, token }) {
      // Extract token from the URL or use the token parameter
      const verificationToken = token || url.split("token=")[1]?.split("&")[0];

      // Create our custom verification URL that points to our frontend page
      const customVerifyLink =
        process.env.NODE_ENV === "development"
          ? `http://localhost:3000/verify-email/${verificationToken}`
          : `${process.env.FRONTEND_URL || "https://app.centaly.com"}/verify-email/${verificationToken}`;

      await resend.emails.send({
        from,
        to: user.email,
        subject: "Verify your email address",
        react: reactVerifyEmail({
          email: user.email,
          verifyLink: customVerifyLink
        })
      });
    }
  },

  plugins: [
    admin({
      adminRoles: ["admin", "owner"]
    }),
    organization({
      cancelPendingInvitationsOnReInvite: true,
      async sendInvitationEmail(data) {
        try {
          await resend.emails.send({
            from,
            to: data.email,
            subject: "You've been invited to join an organization",
            react: reactInvitationEmail({
              invitedByName: data.inviter.user.name,
              organizationName: data.organization.name,
              inviteLink:
                process.env.NODE_ENV === "development"
                  ? `http://localhost:3000/onboarding/accept-invitation/${data.id}`
                  : `${process.env.FRONTEND_URL || "https://app.centaly.com"}/onboarding/accept-invitation/${data.id}`
            })
          });
        } catch (error) {
          console.error("Failed to send invitation email:", error);
          throw error;
        }
      }
    }),
    customSession(async ({ user, session }) => {
      const fullUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: { onboardingStatus: true }
      });
      return {
        user: {
          ...user,
          email: user.email,
          image: user.image,
          emailVerified: user.emailVerified,
          onboardingStatus: fullUser?.onboardingStatus
        },
        session
      };
    })
  ]
});
