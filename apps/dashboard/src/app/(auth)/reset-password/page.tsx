import * as React from "react";
import { redirect } from "next/navigation";
import ResetPasswordForm from "@/src/features/auth/components/reset-password-form";
import { getAuthenticatedUserRedirect } from "@/src/features/auth/dal";
import { AuthContainer } from "@repo/ui/components/auth-container";

export default async function ResetPasswordPage() {
  // Single API call that handles all auth checks (session, email verification, onboarding, organizations)
  // Returns "/sign-in" only if user is not authenticated (no session or API error)
  // Returns other destinations if user has valid session but needs redirecting
  const redirectTo = await getAuthenticatedUserRedirect();

  if (redirectTo !== "/sign-in") {
    // User has valid session and should be redirected to appropriate destination
    // (workspace, onboarding, verification, etc.)
    redirect(redirectTo);
  }

  return (
    <AuthContainer maxWidth="md">
      <ResetPasswordForm />
    </AuthContainer>
  );
}
