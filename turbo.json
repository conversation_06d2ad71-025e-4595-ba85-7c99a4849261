{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "noUpdateNotifier": true, "tasks": {"build": {"env": ["NODE_ENV", "VERCEL_ENV", "VERCEL_URL", "DATABASE_URL", "RESEND_API_KEY", "EMAIL_FROM", "DATABASE_URL", "BETTER_AUTH_SECRET", "BETTER_AUTH_URL", "FRONTEND_URL", "API_URL", "COOKIE_DOMAIN", "ALLOWED_IPS", "MARKETING_URL", "AWS_S3_BUCKET_NAME", "AWS_REGION", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"], "inputs": ["$TURBO_DEFAULT$", ".env"], "dependsOn": ["^build", "db:generate"], "outputs": [".next/**", "!.next/cache/**", "next-env.d.ts", ".expo/**", "dist/**", "build/**", "lib/**"]}, "start": {"cache": false}, "dev": {"inputs": ["$TURBO_DEFAULT$", ".env"], "cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "env": ["NEXT_PUBLIC_API_URL", "NEXT_PUBLIC_APP_URL", "PORT", "LOG_LEVEL"]}, "typecheck": {"dependsOn": ["^typecheck"]}, "db:generate": {"cache": false}}}