import { cache } from "react";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

// Type definitions for Better-Auth session structure
interface User {
  id: string;
  email: string;
  name: string | null;
  image: string | null;
  emailVerified: boolean;
  onboardingStatus: "incomplete" | "workspace" | "invite" | "complete";
  defaultWorkspace?: string;
}

interface Session {
  user: User;
  session: {
    id: string;
    expiresAt: string;
    ipAddress?: string;
    userAgent?: string;
  };
}

interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string | null;
  createdAt: string;
  metadata?: Record<string, unknown>;
  userRole: "member" | "admin" | "owner";
}

// Cache the session verification to avoid duplicate requests within the same render cycle
export const verifySession = cache(async (): Promise<Session | null> => {
  try {
    // Get headers asynchronously (Next.js 15 requirement)
    const headersList = await headers();

    // Make server-to-server call to <PERSON><PERSON> backend
    // This calls the Better-Auth get-session endpoint
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/auth/get-session`,
      {
        headers: {
          // Forward cookies from browser request
          Cookie: headersList.get("cookie") || "",
          // Forward other important headers for security
          "X-Forwarded-For": headersList.get("x-forwarded-for") || "",
          "User-Agent": headersList.get("user-agent") || ""
        },
        cache: "no-store", // Disable Next.js cache to always get fresh session
        credentials: "include" // Include cookies in the request
      }
    );

    if (!response.ok) {
      return null;
    }

    const data = await response.json();

    // Validate the response structure
    if (!data?.user || !data?.session) {
      return null;
    }

    return data as Session;
  } catch (error) {
    console.error("Session verification error:", error);
    return null;
  }
});

// Verify user is authenticated
export async function requireAuth(): Promise<Session> {
  const session = await verifySession();

  if (!session?.user) {
    redirect("/sign-in");
  }

  return session;
}

// Verify user is authenticated and email is verified
export async function requireVerifiedUser(): Promise<Session> {
  const session = await requireAuth();

  if (!session.user.emailVerified) {
    redirect("/verify");
  }

  return session;
}

// Verify user has completed onboarding
export async function requireOnboardedUser(): Promise<Session> {
  const session = await requireVerifiedUser();

  if (session.user.onboardingStatus !== "complete") {
    // Redirect based on current onboarding status
    switch (session.user.onboardingStatus) {
      case "incomplete":
        redirect("/onboarding/workspace");
        break;
      case "workspace":
        redirect("/onboarding/invite");
        break;
      case "invite":
        // If status is invite, they should complete the invite step
        redirect("/onboarding/invite");
        break;
      default:
        // Fallback to workspace creation
        redirect("/onboarding/workspace");
    }
  }

  return session;
}

// Get user's organizations with caching
export const getUserOrganizations = cache(async (): Promise<Organization[]> => {
  await requireAuth();

  try {
    // Get headers asynchronously (Next.js 15 requirement)
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/profile/organizations`,
      {
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        cache: "no-store",
        credentials: "include"
      }
    );

    if (!response.ok) {
      console.error(
        `Failed to fetch organizations: ${response.status} ${response.statusText}`
      );
      return [];
    }

    const data = await response.json();
    return data.organizations || [];
  } catch (error) {
    console.error("Failed to fetch organizations:", error);
    return [];
  }
});

// Verify user has access to a specific workspace
export async function requireWorkspaceAccess(slug: string): Promise<{
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;
}> {
  const session = await requireOnboardedUser();
  const organizations = await getUserOrganizations();

  const currentWorkspace = organizations.find((org) => org.slug === slug);

  if (!currentWorkspace) {
    // User doesn't have access to this workspace
    redirect("/");
  }

  return { session, organizations, currentWorkspace };
}

// Get current workspace without requiring access (for checking)
export async function getWorkspace(slug: string): Promise<Organization | null> {
  const session = await verifySession();

  if (!session) {
    return null;
  }

  const organizations = await getUserOrganizations();
  return organizations.find((org) => org.slug === slug) || null;
}

// Check if user can access a workspace without redirecting
export async function canAccessWorkspace(slug: string): Promise<boolean> {
  try {
    const session = await verifySession();
    if (!session) return false;

    const organizations = await getUserOrganizations();
    return organizations.some((org) => org.slug === slug);
  } catch {
    return false;
  }
}

// Get user's default workspace
export async function getDefaultWorkspace(): Promise<Organization | null> {
  const session = await requireOnboardedUser();
  const organizations = await getUserOrganizations();

  if (session.user.defaultWorkspace) {
    return (
      organizations.find((org) => org.slug === session.user.defaultWorkspace) ||
      null
    );
  }

  // Return first organization if no default is set
  return organizations[0] || null;
}

// Optimized auth redirect handler - performs all auth checks in minimal API calls
// Returns "/sign-in" for unauthenticated users or API errors
// Returns appropriate destination for authenticated users (workspace, onboarding, verification)
// This function is designed for performance - use in auth pages to avoid duplicate API calls
export async function getAuthenticatedUserRedirect(): Promise<string> {
  const session = await verifySession();

  if (!session?.user) {
    // No valid session - user needs to sign in
    return "/sign-in";
  }

  // Check if user needs to verify email
  if (!session.user.emailVerified) {
    return "/verify";
  }

  // Check if user needs to complete onboarding
  if (session.user.onboardingStatus !== "complete") {
    switch (session.user.onboardingStatus) {
      case "incomplete":
        return "/onboarding/workspace";
      case "workspace":
        return "/onboarding/invite";
      case "invite":
        return "/onboarding/invite";
      default:
        return "/onboarding/workspace";
    }
  }

  // User is fully onboarded, get their organizations
  const organizations = await getUserOrganizations();

  // If user has no organizations, send to onboarding
  if (organizations.length === 0) {
    return "/onboarding/workspace";
  }

  // If user has a default workspace, redirect there
  if (session.user.defaultWorkspace) {
    return `/${session.user.defaultWorkspace}/home`;
  }

  // Otherwise, redirect to first organization
  return `/${organizations[0]?.slug}/home`;
}

// Optimized workspace data fetcher for pages reached via getAuthenticatedUserRedirect
// Assumes user already has valid access since they were properly redirected - avoids redundant auth checks
// Use this instead of requireWorkspaceAccess for better performance in auth-protected routes
export async function getWorkspaceData(slug: string): Promise<{
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;
}> {
  // Since this page was reached via our smart redirect logic, we can trust that:
  // 1. User is authenticated and verified
  // 2. User has completed onboarding
  // 3. User has access to this workspace

  // Use the cached session and organizations (these should hit cache if called recently)
  const session = await verifySession();
  const organizations = await getUserOrganizations();

  if (!session?.user) {
    // This shouldn't happen given our redirect logic, but handle gracefully
    redirect("/sign-in");
  }

  const currentWorkspace = organizations.find((org) => org.slug === slug);

  if (!currentWorkspace) {
    // This also shouldn't happen, but handle gracefully by redirecting to home
    redirect("/");
  }

  return { session, organizations, currentWorkspace };
}

// Verify user has specific role in workspace
export async function requireWorkspaceRole(
  slug: string,
  requiredRole: "member" | "admin" | "owner"
): Promise<{
  session: Session;
  workspace: Organization;
}> {
  const { session, currentWorkspace } = await requireWorkspaceAccess(slug);

  if (!currentWorkspace) {
    redirect("/");
  }

  const roleHierarchy = { member: 0, admin: 1, owner: 2 };
  const userRoleLevel = roleHierarchy[currentWorkspace.userRole];
  const requiredRoleLevel = roleHierarchy[requiredRole];

  if (userRoleLevel < requiredRoleLevel) {
    // User doesn't have sufficient permissions
    redirect("/");
  }

  return { session, workspace: currentWorkspace };
}

// Utility function to check if user has a specific permission in workspace
export async function hasWorkspacePermission(
  slug: string,
  permission: "read" | "write" | "admin"
): Promise<boolean> {
  try {
    const workspace = await getWorkspace(slug);
    if (!workspace) return false;

    const { userRole } = workspace;

    switch (permission) {
      case "read":
        return ["member", "admin", "owner"].includes(userRole);
      case "write":
        return ["admin", "owner"].includes(userRole);
      case "admin":
        return userRole === "owner";
      default:
        return false;
    }
  } catch {
    return false;
  }
}

// Export types for use in other components
export type { Session, User, Organization };
