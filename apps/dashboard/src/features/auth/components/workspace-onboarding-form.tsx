/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {
  updateDefaultWorkspace,
  updateOnboardingStatus
} from "@/src/lib/api/onboarding";
import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

// Simplified schema without async validation
const workspaceOnboardingSchema = z.object({
  name: z.string().min(1, "Workspace name is required"),
  slug: z
    .string()
    .min(3, "Slug must be at least 3 characters")
    .max(48, "Slug must be no more than 48 characters")
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      "Slug can only contain lowercase letters, numbers, and hyphens"
    )
});

export type WorkspaceOnboardingFormData = z.infer<
  typeof workspaceOnboardingSchema
>;

const WorkspaceOnboardingForm = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const router = useRouter();

  const methods = useForm<WorkspaceOnboardingFormData>({
    resolver: zodResolver(workspaceOnboardingSchema),
    defaultValues: {
      name: "",
      slug: ""
    },
    mode: "onSubmit"
  });

  const onSubmit = async (data: WorkspaceOnboardingFormData) => {
    setIsLoading(true);
    setError("");

    try {
      // First, check if the slug is available
      const slugCheckResult = await authClient.organization.checkSlug({
        slug: data.slug
      });

      // If slug is not available, show error
      if (!slugCheckResult.data?.status) {
        setError(
          "This workspace URL is already taken. Please choose a different one."
        );
        setIsLoading(false);
        return;
      }

      // Create organization
      const createResult = await authClient.organization.create({
        name: data.name,
        slug: data.slug
      });

      if (createResult.error) {
        throw new Error(
          createResult.error.message || "Failed to create organization"
        );
      }

      if (createResult.data) {
        // Clear form state
        methods.reset();
        setError("");

        // Batch all the post-creation operations to minimize API calls
        try {
          // Perform all operations in parallel where possible
          await Promise.all([
            // Set the newly created organization as active
            authClient.organization.setActive({
              organizationId: createResult.data.id
            }),
            // Update onboarding status to 'invite'
            updateOnboardingStatus("invite"),
            // Set as default workspace
            updateDefaultWorkspace(data.slug)
          ]);

          // Redirect to invite onboarding page
          router.push("/onboarding/invite");
        } catch (postCreationError) {
          console.error(
            "Error in post-creation operations:",
            postCreationError
          );
          // Even if some post-creation operations fail, the organization was created
          // so we should still redirect to the next step
          router.push("/onboarding/invite");
        }
      }
    } catch (err: any) {
      setIsLoading(false);

      // Extract error information
      const status = err?.status || 0;
      const message = err?.message || "";

      let errorMessage = "An unexpected error occurred. Please try again.";

      // Handle specific error cases with user-friendly messages
      if (status === 400) {
        // Bad request errors
        if (
          message.toLowerCase().includes("slug") &&
          message.toLowerCase().includes("taken")
        ) {
          errorMessage =
            "This workspace URL is already taken. Please choose a different one.";
        } else if (
          message.toLowerCase().includes("slug") &&
          message.toLowerCase().includes("invalid")
        ) {
          errorMessage =
            "The workspace URL contains invalid characters. Please use only lowercase letters, numbers, and hyphens.";
        } else if (
          message.toLowerCase().includes("name") &&
          message.toLowerCase().includes("required")
        ) {
          errorMessage = "Workspace name is required.";
        } else if (
          message.toLowerCase().includes("name") &&
          message.toLowerCase().includes("long")
        ) {
          errorMessage =
            "Workspace name is too long. Please use a shorter name.";
        } else if (
          message.toLowerCase().includes("organization") &&
          message.toLowerCase().includes("limit")
        ) {
          errorMessage =
            "You've reached the maximum number of workspaces allowed. Please contact support to increase your limit.";
        } else {
          errorMessage = "Please check your input and try again.";
        }
      } else if (status === 401) {
        // Unauthorized
        errorMessage =
          "You're not authorized to create workspaces. Please sign in again.";
      } else if (status === 403) {
        // Forbidden
        if (message.toLowerCase().includes("permission")) {
          errorMessage =
            "You don't have permission to create workspaces. Please contact your administrator.";
        } else if (
          message.toLowerCase().includes("plan") ||
          message.toLowerCase().includes("subscription")
        ) {
          errorMessage =
            "Your current plan doesn't allow creating workspaces. Please upgrade your plan.";
        } else {
          errorMessage =
            "You're not allowed to create workspaces at this time.";
        }
      } else if (status === 409) {
        // Conflict
        errorMessage =
          "A workspace with this name or URL already exists. Please choose different values.";
      } else if (status === 422) {
        // Validation error
        errorMessage =
          "Please check your input. The workspace name or URL format is invalid.";
      } else if (status === 429) {
        // Rate limit
        errorMessage = "Too many requests. Please wait a moment and try again.";
      } else if (status >= 500) {
        // Server errors
        errorMessage =
          "We're experiencing technical difficulties. Please try again in a few minutes.";
      } else if (status === 0 || !status) {
        // Network error
        errorMessage =
          "Unable to connect to the server. Please check your internet connection and try again.";
      } else if (
        err?.message?.includes("401") ||
        err?.message?.includes("unauthorized")
      ) {
        errorMessage =
          "You're not signed in. Please sign in again and try creating your workspace.";
      } else if (err?.message?.includes("slug")) {
        errorMessage =
          "This workspace URL is already taken. Please choose a different one.";
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    }
  };

  // Clear errors when user starts typing
  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
  };

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <h1 className="text-3xl pb-4">Create your workspace</h1>
        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-4"
          >
            <FormField
              control={methods.control}
              name="name"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Workspace name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="text"
                      placeholder="Enter your workspace name"
                      maxLength={255}
                      autoCapitalize="off"
                      disabled={isLoading}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={methods.control}
              name="slug"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Workspace URL</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      id="slug"
                      type="text"
                      required
                      placeholder="acme"
                      minLength={3}
                      maxLength={48}
                      autoCapitalize="off"
                      disabled={isLoading}
                      onChange={(e) => {
                        field.onChange(e);
                        clearErrorsOnChange();
                      }}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">
                    This will be your workspace URL: app.centaly.com/
                    {field.value || "workspace"}
                  </p>
                  <FormMessage />
                </FormItem>
              )}
            />

            {error && (
              <>
                <p className="text-red-500 text-sm">{error}</p>
              </>
            )}

            <Button
              type="submit"
              size="lg"
              className="w-full relative"
              loading={isLoading}
              onClick={methods.handleSubmit(onSubmit)}
            >
              Create workspace
            </Button>
          </form>
        </FormProvider>
      </Card>
    </main>
  );
};

export default WorkspaceOnboardingForm;
