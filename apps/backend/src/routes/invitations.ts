import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import { schemas, type Variables } from "../lib/openapi.js";

// Invitation validation schema
const invitationValidationSchema = z.object({
  valid: z.boolean(),
  message: z.string().optional(),
  email: z.string().optional()
});

// Create invitations router
export const invitationsRouter = new OpenAPIHono<{ Variables: Variables }>();

// Validate invitation (public endpoint)
const validateInvitationRoute = createRoute({
  method: "get",
  path: "/invitations/{id}/validate",
  tags: ["Invitations"],
  summary: "Validate invitation",
  description:
    "Publicly validate if an invitation exists and is valid (without authentication)",
  request: {
    params: z.object({
      id: z.string().min(1)
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: invitationValidationSchema
        }
      },
      description: "Invitation validation result"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Invitation not found"
    }
  }
});

invitationsRouter.openapi(validateInvitationRoute, async (c) => {
  const { id } = c.req.param();

  try {
    const invitation = await prisma.invitation.findUnique({
      where: { id },
      select: {
        id: true,
        status: true,
        expiresAt: true,
        email: true
      }
    });

    if (!invitation) {
      return c.json(
        {
          valid: false,
          message: "Invitation not found"
        },
        200
      );
    }

    // Check if invitation is still valid
    const now = new Date();
    const isExpired = invitation.expiresAt < now;
    const isNotPending = invitation.status !== "pending";

    if (isExpired) {
      return c.json(
        {
          valid: false,
          message: "Invitation has expired"
        },
        200
      );
    }

    if (isNotPending) {
      return c.json(
        {
          valid: false,
          message: `Invitation is ${invitation.status}`
        },
        200
      );
    }

    return c.json(
      {
        valid: true,
        message: "Invitation is valid",
        email: invitation.email
      },
      200
    );
  } catch (error) {
    console.error("Error validating invitation:", error);
    return c.json(
      {
        valid: false,
        message: "Error validating invitation"
      },
      200
    );
  }
});
