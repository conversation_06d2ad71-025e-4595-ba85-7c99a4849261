/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Preview,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

export type ConnectedAccountSecurityAlertEmailProps = {
  appName: string;
  name: string;
  provider: string;
  action: "connected" | "disconnected";
};

const HtmlCompat = Html as any;
const HeadCompat = Head as any;
const PreviewCompat = Preview as any;
const BodyCompat = Body as any;
const ContainerCompat = Container as any;
const HeadingCompat = Heading as any;
const TextCompat = Text as any;
const HrCompat = Hr as any;

export function ConnectedAccountSecurityAlertEmail({
  appName,
  name,
  provider,
  action,
}: ConnectedAccountSecurityAlertEmailProps): React.JSX.Element {
  return (
    <HtmlCompat>
      <HeadCompat />
      <PreviewCompat>Security Alert!</PreviewCompat>
      <Tailwind>
        <BodyCompat className="m-auto bg-white px-2 font-sans">
          <ContainerCompat className="mx-auto my-[40px] max-w-[465px] rounded border border-solid border-[#eaeaea] p-[20px]">
            <HeadingCompat className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
              Security Alert!
            </HeadingCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              Hello {name},
            </TextCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              The login &quot;{provider}&quot; has been {action}{" "}
              {action === "disconnected" ? "from" : "to"} your account.
            </TextCompat>
            <HrCompat className="mx-0 my-[26px] w-full border border-solid border-[#eaeaea]" />
            <TextCompat className="text-[12px] leading-[24px] text-[#666666]">
              You receive this message because there has been account security
              changes on {appName}.
            </TextCompat>
          </ContainerCompat>
        </BodyCompat>
      </Tailwind>
    </HtmlCompat>
  );
}
