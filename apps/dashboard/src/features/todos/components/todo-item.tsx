"use client";

import { useState } from "react";
import { Trash2, Edit3, Check, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@repo/ui/components/button";
import { Checkbox } from "@repo/ui/components/checkbox";
import { Input } from "@repo/ui/components/input";
import { cn } from "@repo/ui/utils/cn";

import type { Todo } from "../dal/todos";
import { useUpdateTodo, useDeleteTodo } from "../hooks/use-todos";
import { updateTodoSchema, type UpdateTodoFormData } from "../schemas/todo-schema";

interface TodoItemProps {
  todo: Todo;
  organizationId: string;
}

export function TodoItem({ todo, organizationId }: TodoItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const updateTodoMutation = useUpdateTodo(organizationId);
  const deleteTodoMutation = useDeleteTodo(organizationId);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<UpdateTodoFormData>({
    resolver: zodResolver(updateTodoSchema),
    defaultValues: {
      text: todo.text
    }
  });

  const handleToggleComplete = () => {
    updateTodoMutation.mutate({
      todoId: todo.id,
      data: { completed: !todo.completed }
    });
  };

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this todo?")) {
      deleteTodoMutation.mutate(todo.id);
    }
  };

  const handleStartEdit = () => {
    reset({ text: todo.text });
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    reset({ text: todo.text });
  };

  const handleSaveEdit = (data: UpdateTodoFormData) => {
    updateTodoMutation.mutate({
      todoId: todo.id,
      data: { text: data.text }
    }, {
      onSuccess: () => {
        setIsEditing(false);
      }
    });
  };

  const isLoading = updateTodoMutation.isPending || deleteTodoMutation.isPending;

  return (
    <div className={cn(
      "flex items-center gap-3 p-4 border rounded-lg bg-white",
      isLoading && "opacity-50 pointer-events-none"
    )}>
      <Checkbox
        checked={todo.completed}
        onCheckedChange={handleToggleComplete}
        disabled={isLoading}
        className="mt-0.5"
      />

      <div className="flex-1 min-w-0">
        {isEditing ? (
          <form onSubmit={handleSubmit(handleSaveEdit)} className="flex items-center gap-2">
            <div className="flex-1">
              <Input
                {...register("text")}
                placeholder="Enter todo text..."
                className={cn(
                  "h-8",
                  errors.text && "border-red-500"
                )}
                autoFocus
              />
              {errors.text && (
                <p className="text-sm text-red-500 mt-1">{errors.text.message}</p>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Button
                type="submit"
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                disabled={updateTodoMutation.isPending}
              >
                <Check className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                className="h-8 w-8 p-0"
                onClick={handleCancelEdit}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </form>
        ) : (
          <p className={cn(
            "text-sm break-words",
            todo.completed && "text-gray-500 line-through"
          )}>
            {todo.text}
          </p>
        )}
      </div>

      {!isEditing && (
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={handleStartEdit}
            disabled={isLoading}
          >
            <Edit3 className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            onClick={handleDelete}
            disabled={isLoading}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}