{"name": "@repo/authentication", "version": "0.0.0", "type": "module", "packageManager": "pnpm@9.15.4", "exports": {"./auth": "./src/lib/auth.ts", "./auth-client": "./src/lib/auth-client.ts", "./types/auth": "./src/types/auth.ts"}, "scripts": {"lint": "eslint .", "typecheck": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2"}, "dependencies": {"@repo/database": "workspace:*", "@repo/email": "workspace:*", "better-auth": "^1.2.12", "dotenv": "^17.1.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "prettier": "@repo/prettier-config"}