/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import {
  <PERSON>,
  Button,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

// Type assertion workaround for React 19 compatibility
const HtmlCompat = Html as any;
const ButtonCompat = Button as any;
const HeadCompat = Head as any;
const PreviewCompat = Preview as any;
const BodyCompat = Body as any;
const ContainerCompat = Container as any;
const HeadingCompat = Heading as any;
const TextCompat = Text as any;
const SectionCompat = Section as any;
const LinkCompat = Link as any;
const HrCompat = Hr as any;

export type ConfirmEmailAddressChangeEmailProps = {
  name: string;
  confirmLink: string;
};

export function ConfirmEmailAddressChangeEmail({
  name,
  confirmLink,
}: ConfirmEmailAddressChangeEmailProps): React.JSX.Element {
  return (
    <HtmlCompat>
      <HeadCompat />
      <PreviewCompat>Confirm new email address</PreviewCompat>
      <Tailwind>
        <BodyCompat className="m-auto bg-white px-2 font-sans">
          <ContainerCompat className="mx-auto my-[40px] max-w-[465px] rounded border border-solid border-[#eaeaea] p-[20px]">
            <HeadingCompat className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
              Confirm new email address
            </HeadingCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              Hello {name},
            </TextCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              To complete your email address change request, you must confirm
              your new email address.
            </TextCompat>
            <SectionCompat className="my-[32px] text-center">
              <ButtonCompat
                href={confirmLink}
                className="rounded bg-[#000000] px-5 py-3 text-center text-[12px] font-semibold text-white no-underline"
              >
                Confirm new email
              </ButtonCompat>
            </SectionCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              or copy and paste this URL into your browser:{" "}
              <LinkCompat
                href={confirmLink}
                className="text-blue-600 no-underline"
              >
                {confirmLink}
              </LinkCompat>
            </TextCompat>
            <HrCompat className="mx-0 my-[26px] w-full border border-solid border-[#eaeaea]" />
            <TextCompat className="text-[12px] leading-[24px] text-[#666666]">
              If you don&apos;t want to change your email address or didn&apos;t
              request this, just ignore and delete this message. To keep your
              account secure, please don&apos;t forward this email to anyone.
            </TextCompat>
          </ContainerCompat>
        </BodyCompat>
      </Tailwind>
    </HtmlCompat>
  );
}
