#!/bin/bash

# Deploy backend to production environment
echo "🚀 Deploying backend to production environment..."

# Ensure we're in the project root
if [ ! -f "fly.backend.toml" ]; then
    echo "❌ Error: fly.backend.toml not found. Make sure you're in the project root."
    exit 1
fi

# Confirmation prompt for production
read -p "⚠️  Are you sure you want to deploy to PRODUCTION? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "🛑 Production deployment cancelled."
    exit 0
fi

# Deploy to production
fly deploy --config fly.backend.toml

if [ $? -eq 0 ]; then
    echo "✅ Production deployment successful!"
    echo "🌐 Production URL: https://centaly-backend.fly.dev"
    echo "🔍 Health check: curl https://centaly-backend.fly.dev/health"
else
    echo "❌ Production deployment failed!"
    exit 1
fi 