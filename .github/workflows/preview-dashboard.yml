name: Dashboard Preview (Staging)
env:
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
  TURBO_CACHE: "remote:rw"
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]
    paths:
      - "apps/dashboard/**"
      - "packages/**"
      - "turbo.json"
      - "package.json"
      - "pnpm-lock.yaml"

permissions:
  contents: read
  pull-requests: write
  issues: write
  deployments: write

concurrency:
  group: dashboard-pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true

jobs:
  deploy-preview:
    name: Deploy Preview to Vercel (Staging)
    runs-on: ubuntu-latest
    environment: preview
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install

      - name: Generate Prisma Client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Build and Deploy to Vercel
        id: deploy
        env:
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}

        run: |
          pnpm dlx vercel pull --yes --environment=staging --token=$VERCEL_TOKEN
          pnpm dlx vercel build --target=staging --token=$VERCEL_TOKEN
          DEPLOYMENT_URL=$(pnpm dlx vercel deploy --prebuilt --target=staging --token=$VERCEL_TOKEN)
          echo "deployment_url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          echo "✅ Deployed to $DEPLOYMENT_URL"

      - name: Comment deployment status
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.issue.number;
            const previewUrl = '${{ steps.deploy.outputs.deployment_url }}';

            await github.rest.issues.createComment({
              issue_number: prNumber,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Dashboard PR Preview Deployed!**\n\n📱 **Preview URL**: https://staging.centaly.com\n\n*This preview is deployed to staging.centaly.com and will be automatically updated when the PR is updated. Production remains unaffected at https://app.centaly.com*\n\n${context.payload.pull_request.labels.some(label => label.name === 'database-migration') ? '🗄️ **Database schema changes were pushed to preview environment**' : ''}`
            });
