/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState } from "react";
import { updateOnboardingStatus } from "@/src/lib/api/onboarding";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  authClient,
  useActiveOrganization
} from "@repo/authentication/auth-client";
import { AuthContainer } from "@repo/ui/components/auth-container";
import { Button } from "@repo/ui/components/button";
import { Card } from "@repo/ui/components/card";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormProvider
} from "@repo/ui/components/form";
import { Input } from "@repo/ui/components/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@repo/ui/components/select";
import { Spinner } from "@repo/ui/components/spinner";
import { useForm } from "react-hook-form";
import { z } from "zod";

import OnboardingCompletionLoader from "./onboarding-completion-loader";

const roleOptions = [
  { value: "member", label: "Member" },
  { value: "admin", label: "Admin" },
  { value: "owner", label: "Owner" }
] as const;

const inviteSchema = z.object({
  invites: z
    .array(
      z.object({
        email: z
          .string()
          .email("Invalid email address")
          .optional()
          .or(z.literal("")),
        role: z.enum(["member", "admin", "owner"]).optional()
      })
    )
    .length(3)
    .refine(
      (invites) => {
        const validEmails = invites.filter(
          (invite) => invite.email && invite.email.trim() !== ""
        );
        return validEmails.length > 0;
      },
      {
        message: "Please add at least one email address to continue",
        path: ["invites"]
      }
    )
});

type InviteFormData = z.infer<typeof inviteSchema>;

const InviteOnboardingForm = () => {
  const [loadingState, setLoadingState] = useState<boolean>(false);
  const [showCompletionLoader, setShowCompletionLoader] =
    useState<boolean>(false);
  const [error, setError] = useState<string>("");

  // Use Better-Auth hook to get active organization
  const { data: activeOrganization } = useActiveOrganization();

  const methods = useForm<InviteFormData>({
    resolver: zodResolver(inviteSchema),
    defaultValues: {
      invites: [
        { email: "", role: "member" },
        { email: "", role: "member" },
        { email: "", role: "member" }
      ]
    },
    mode: "onSubmit"
  });

  const onSubmit = async (data: InviteFormData) => {
    if (!activeOrganization) {
      setError(
        "No active organization found. Please create a workspace first."
      );
      return;
    }

    setLoadingState(true);
    setError("");

    try {
      const validInvites = data.invites.filter(
        (invite) => invite.email && invite.email.trim() !== ""
      );

      // Send invitations in parallel for better performance
      const invitePromises = validInvites.map(async (invite) => {
        const result = await authClient.organization.inviteMember({
          email: invite.email!,
          role: invite.role!,
          organizationId: activeOrganization.id
        });
        return result;
      });

      await Promise.all(invitePromises);

      // Mark onboarding as complete
      await updateOnboardingStatus("complete");

      // Show completion loader which will handle the redirect
      setLoadingState(false);
      setShowCompletionLoader(true);
    } catch (err: any) {
      let errorMessage = "Failed to send invitations";

      if (err?.message?.includes("already exists")) {
        errorMessage =
          "One or more users are already members of this workspace";
      } else if (err?.message?.includes("invalid email")) {
        errorMessage = "Please check the email addresses and try again";
      } else if (err?.message?.includes("rate limit")) {
        errorMessage = "Too many requests. Please try again later";
      } else if (err?.message?.includes("organization")) {
        errorMessage = "Organization not found. Please refresh and try again.";
      } else if (err?.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoadingState(false);
    }
  };

  const clearErrorsOnChange = () => {
    if (error) {
      setError("");
    }
    // Clear form validation errors when user starts typing
    if (methods.formState.errors.invites) {
      methods.clearErrors("invites");
    }
  };

  const handleSkip = async () => {
    try {
      setLoadingState(true);
      // Mark onboarding as complete even when skipping
      await updateOnboardingStatus("complete");

      // Show completion loader which will handle the redirect
      setLoadingState(false);
      setShowCompletionLoader(true);
    } catch (err) {
      console.error("Error skipping onboarding:", err);
      setError("Failed to complete onboarding. Please try again.");
      setLoadingState(false);
    }
  };

  // Show loading state while organization is being fetched
  if (!activeOrganization) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex justify-center">
          <Spinner />
        </div>
      </AuthContainer>
    );
  }

  // Show completion loader when onboarding is complete
  if (showCompletionLoader) {
    return (
      <AuthContainer maxWidth="md">
        <div className="flex justify-center">
          <OnboardingCompletionLoader />
        </div>
      </AuthContainer>
    );
  }

  return (
    <main>
      <Card className="w-full max-w-lg p-6 space-y-6">
        <div>
          <h1 className="text-3xl pb-4">Invite team members</h1>
          <p className="text-muted-foreground">
            Add team members to collaborate on your workspace. You can skip this
            step and invite members later.
          </p>
        </div>

        {error && <p className="text-destructive">{error}</p>}

        <FormProvider {...methods}>
          <form
            onSubmit={methods.handleSubmit(onSubmit)}
            className="space-y-6"
          >
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={index}
                  className="flex gap-4"
                >
                  <FormField
                    control={methods.control}
                    name={`invites.${index}.email`}
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        {index === 0 && <FormLabel>Email address</FormLabel>}
                        <FormControl>
                          <Input
                            {...field}
                            type="email"
                            placeholder="<EMAIL>"
                            maxLength={255}
                            autoCapitalize="off"
                            disabled={loadingState}
                            onChange={(e) => {
                              field.onChange(e);
                              clearErrorsOnChange();
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={methods.control}
                    name={`invites.${index}.role`}
                    render={({ field }) => (
                      <FormItem className="flex-shrink-0">
                        {index === 0 && <FormLabel>Role</FormLabel>}
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={loadingState}
                        >
                          <FormControl>
                            <SelectTrigger className="w-32">
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roleOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              ))}

              {/* Display validation error for the invites array */}
              {methods.formState.errors.invites && (
                <p className="text-sm text-destructive">
                  {methods.formState.errors.invites.message}
                </p>
              )}
            </div>

            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                size="lg"
                className="flex-1"
                onClick={handleSkip}
                disabled={loadingState}
              >
                Skip for now
              </Button>
              <Button
                type="submit"
                size="lg"
                className="flex-1"
                loading={loadingState}
              >
                Continue
              </Button>
            </div>
          </form>
        </FormProvider>
      </Card>
    </main>
  );
};

export default InviteOnboardingForm;
