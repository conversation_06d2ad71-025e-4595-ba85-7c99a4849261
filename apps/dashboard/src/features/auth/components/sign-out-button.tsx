"use client";

import { useRouter } from "next/navigation";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";

interface SignOutButtonProps {
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
}

export default function SignOutButton({
  variant = "link",
  size = "default",
  className,
  children = "Sign out"
}: SignOutButtonProps) {
  const router = useRouter();

  const handleSignOut = async () => {
    await authClient.signOut({
      fetchOptions: {
        onSuccess: () => {
          // Redirect to sign-in page after successful sign out
          router.push("/sign-in");
        }
      }
    });
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={handleSignOut}
    >
      {children}
    </Button>
  );
}
