import * as React from "react";

import { cn } from "../utils/cn";

interface InputProps extends React.ComponentProps<"input"> {
  label?: string;
  helperText?: string;
  error?: string;
  wrapperClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    { className, type, label, helperText, error, wrapperClassName, ...props },
    ref
  ) => {
    const id = React.useId();

    return (
      <div className={cn("space-y-1 w-full", wrapperClassName)}>
        {label && (
          <label className="text-xs leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-muted-foreground">
            {label}
          </label>
        )}
        <input
          type={type}
          id={props.id || id}
          className={cn(
            "flex h-10 w-full rounded-sm border border-input bg-background px-3 py-1 transition-colors text-sm",
            "file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground",
            "placeholder:text-muted-foreground",
            "focus-visible:border-ring focus-visible:outline-none focus-visible:ring-[3px] focus-visible:ring-ring/20",
            "disabled:cursor-not-allowed disabled:opacity-50",
            "md:text-sm",
            error && "border-red-500 focus-visible:ring-red-500",
            className
          )}
          ref={ref}
          aria-describedby={
            helperText || error ? `${id}-description` : undefined
          }
          aria-invalid={!!error}
          {...props}
        />
        {(helperText || error) && (
          <p
            id={`${id}-description`}
            className={cn(
              "text-xs",
              error ? "text-red-500" : "text-muted-foreground"
            )}
            role="region"
            aria-live="polite"
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
export type { InputProps };
