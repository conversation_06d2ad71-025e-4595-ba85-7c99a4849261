name: Preview Backend Deployment

on:
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened]
    paths:
      - "apps/backend/**"
      - "packages/**"
      - "!packages/ui/**"
      - "turbo.json"
      - "package.json"
      - "pnpm-lock.yaml"
      - "Dockerfile"
      - "fly.staging.toml"

permissions:
  contents: read
  pull-requests: write
  issues: write

concurrency:
  group: staging-backend-${{ github.ref }}
  cancel-in-progress: true

jobs:
  quality-checks:
    name: Quality Checks
    runs-on: ubuntu-latest
    if: github.event.action != 'closed'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma Client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}

      - name: TypeScript type checking
        run: pnpm typecheck --filter=backend

      - name: Build backend
        run: pnpm build --filter=backend

  deploy:
    name: Deploy to Fly.io Staging
    runs-on: ubuntu-latest
    needs: quality-checks
    environment:
      name: staging
      url: https://centaly-backend-staging.fly.dev
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy to Fly.io
        run: flyctl deploy --config fly.staging.toml --remote-only
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Comment deployment status
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.issue.number;
            const deployUrl = '${{ steps.deploy.outputs.url }}';

            await github.rest.issues.createComment({
              issue_number: prNumber,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Backend PR Preview Deployed!**\n\n🔗 **API URL**: https://api-staging.centaly.com\n\n📱 **Frontend URL**: https://staging.centaly.com\n\n*This preview environment is automatically created for PR #${prNumber} and uses the staging environment for both frontend and backend.*`
            });
