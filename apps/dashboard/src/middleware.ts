import { NextRequest, NextResponse } from "next/server";

// Environment variable validation
function validateEnvironment() {
  const required = ["ALLOWED_IPS", "MARKETING_URL"];
  const missing = required.filter((key) => !process.env[key]);

  if (missing.length > 0) {
    console.error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
    return false;
  }
  return true;
}

// IP detection for Edge Runtime
function getClientIP(request: NextRequest): string | null {
  // Get IP from Vercel-provided headers
  const forwardedFor = request.headers.get("x-forwarded-for");
  if (forwardedFor) {
    return forwardedFor.split(",")[0]?.trim() || null;
  }

  const realIp = request.headers.get("x-real-ip");
  if (realIp) {
    return realIp;
  }

  // Fallback to connecting IP
  const connectingIp = request.headers.get("x-vercel-forwarded-for");
  if (connectingIp) {
    return connectingIp;
  }

  return null;
}

// IP validation with exact matching
function isIPAllowed(clientIP: string, allowedIPs: string[]): boolean {
  return allowedIPs.some((allowedIP) => {
    return allowedIP.trim() === clientIP;
  });
}

export async function middleware(request: NextRequest) {
  // Validate environment variables
  if (!validateEnvironment()) {
    return NextResponse.next();
  }

  try {
    // Get client IP
    const clientIP = getClientIP(request);

    if (!clientIP) {
      const marketingUrl = new URL(process.env.MARKETING_URL!);
      return NextResponse.redirect(marketingUrl, { status: 302 });
    }

    // Check IP whitelist
    const allowedIPs = process.env.ALLOWED_IPS!.split(",");

    if (!isIPAllowed(clientIP, allowedIPs)) {
      const marketingUrl = new URL(process.env.MARKETING_URL!);
      return NextResponse.redirect(marketingUrl, { status: 302 });
    }

    // IP is whitelisted, allow access
    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    // Fail safe - allow access if there's an error
    return NextResponse.next();
  }
}

// Optimized matcher to reduce unnecessary invocations
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|public).*)"
  ]
};
