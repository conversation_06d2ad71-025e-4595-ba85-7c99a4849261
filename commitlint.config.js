const Configuration = {
  extends: ["@commitlint/config-conventional"],
  rules: {
    // Custom rules for the project
    "type-enum": [
      2,
      "always",
      [
        "feat", // new feature
        "fix", // bug fix
        "docs", // documentation changes
        "style", // formatting, missing semicolons, etc.
        "refactor", // refactoring production code
        "test", // adding tests, refactoring test code
        "chore", // updating build tasks, package manager configs, etc.
        "perf", // performance improvements
        "ci", // continuous integration related
        "build", // build system or external dependencies
        "revert", // reverting a previous commit
      ],
    ],

    // Enforce conventional format

    "subject-empty": [2, "never"],
    "subject-min-length": [2, "always", 3],
    "subject-max-length": [2, "always", 72],
    "header-max-length": [2, "always", 100],
  },
};

module.exports = Configuration;
