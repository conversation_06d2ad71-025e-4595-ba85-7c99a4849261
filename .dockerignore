# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Build output
dist
build
.next
out

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Docker
.dockerignore

# Testing
coverage
.nyc_output

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Turbo
.turbo

# TypeScript
*.tsbuildinfo

# Apps specific build outputs
apps/dashboard/.next
apps/dashboard/out
apps/backend/dist

# Packages specific build outputs
packages/*/dist
packages/*/build 