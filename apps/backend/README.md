# Centaly Backend API

This is the Hono.js backend API for the Centaly application.

## Features

- 🚀 Fast and lightweight with Hono.js
- 📝 OpenAPI/Swagger documentation
- 🔐 Authentication with Better-Auth
- 🗄️ Database integration with Prisma
- 🛡️ Type-safe environment variables
- 📊 Request logging and error handling
- 🔄 CORS configured for dashboard app

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Type checking
pnpm check-types

# Linting
pnpm lint
```

## API Documentation

When running in development, you can access:

- Swagger UI: http://localhost:3001/api/v1/swagger
- OpenAPI JSON: http://localhost:3001/api/v1/openapi.json
- Health check: http://localhost:3001/health

## Environment Variables

Copy `.env.example` to `.env` and configure:

```bash
cp .env.example .env
```

Required variables:

- `DATABASE_URL`: MySQL (PlanetScale) connection string
- `BETTER_AUTH_SECRET`: Authentication secret (min 32 characters)

## Project Structure

```
src/
├── index.ts          # Main application entry
├── lib/              # Utilities and configuration
│   ├── env.ts        # Environment variables
│   └── openapi.ts    # OpenAPI configuration
└── middleware/       # Custom middleware
    ├── error-handler.ts
    └── request-id.ts
```

## Error Handling

The API uses a centralized error handling system:

- `APIError`: Custom error class for API-specific errors
- Automatic Zod validation error formatting
- Prisma error handling
- Request ID tracking for debugging

## Adding New Routes

Example of adding a typed route with OpenAPI documentation:

```typescript
import { createRoute, z } from "@hono/zod-openapi";

const getUserRoute = createRoute({
  method: "get",
  path: "/users/{id}",
  tags: ["Users"],
  summary: "Get user by ID",
  request: {
    params: z.object({
      id: z.string().uuid(),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: userSchema,
        },
      },
      description: "User found",
    },
    404: {
      content: {
        "application/json": {
          schema: errorSchema,
        },
      },
      description: "User not found",
    },
  },
});

api.openapi(getUserRoute, async (c) => {
  const { id } = c.req.param();
  // Implementation here
});
```
