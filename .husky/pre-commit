#!/bin/sh
. "$(dirname -- "$0")/_/husky.sh"

echo "Running pre-commit checks..."

# Run linting
echo "🔍 Running lint check..."
pnpm lint
if [ $? -ne 0 ]; then
  echo "❌ <PERSON><PERSON> failed. Please fix the issues and try again."
  exit 1
fi

# Run type checking
echo "🔍 Running type check..."
pnpm typecheck
if [ $? -ne 0 ]; then
  echo "❌ Type checking failed. Please fix the issues and try again."
  exit 1
fi

echo "✅ All pre-commit checks passed!"
