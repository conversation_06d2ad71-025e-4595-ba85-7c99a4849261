"use client";

import { useState } from "react";
import { Filter } from "lucide-react";

import { Button } from "@repo/ui/components/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components/select";

import type { Todo } from "../dal/todos";
import { useTodos } from "../hooks/use-todos";
import { TodoItem } from "./todo-item";

interface TodosListProps {
  organizationId: string;
  initialData?: {
    todos: Todo[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  };
}

export function TodosList({ organizationId, initialData }: TodosListProps) {
  const [filter, setFilter] = useState<"all" | "active" | "completed">("all");
  const [page, setPage] = useState(1);

  const filterOptions = {
    all: undefined,
    active: false,
    completed: true
  };

  const { data, isLoading, error } = useTodos(
    organizationId,
    {
      page,
      pageSize: 20,
      completed: filterOptions[filter]
    },
    initialData
  );

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Failed to load todos. Please try again.</p>
      </div>
    );
  }

  if (isLoading && !data) {
    return (
      <div className="space-y-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 animate-pulse rounded-lg" />
        ))}
      </div>
    );
  }

  const todos = data?.todos || [];
  const pagination = data?.pagination;

  return (
    <div className="space-y-4">
      {/* Filter Controls */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <Select value={filter} onValueChange={(value) => {
            setFilter(value as "all" | "active" | "completed");
            setPage(1); // Reset to first page when filter changes
          }}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {pagination && (
          <div className="text-sm text-gray-500">
            {pagination.total} {pagination.total === 1 ? "todo" : "todos"}
          </div>
        )}
      </div>

      {/* Todos List */}
      {todos.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">
            {filter === "all" 
              ? "No todos yet. Create your first todo above!"
              : filter === "active"
              ? "No active todos. Great job!"
              : "No completed todos yet."
            }
          </p>
        </div>
      ) : (
        <div className="space-y-2">
          {todos.map((todo: Todo) => (
            <TodoItem
              key={todo.id}
              todo={todo}
              organizationId={organizationId}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 pt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page - 1)}
            disabled={page === 1 || isLoading}
          >
            Previous
          </Button>
          
          <span className="text-sm text-gray-500">
            Page {page} of {pagination.totalPages}
          </span>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPage(page + 1)}
            disabled={page === pagination.totalPages || isLoading}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
}