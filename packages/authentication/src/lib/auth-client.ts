import {
  adminClient,
  customSessionClient,
  multiSessionClient,
  organizationClient,
  twoFactorClient
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

import { auth } from "./auth.js";

export const authClient = createAuthClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001",
  plugins: [
    customSessionClient<typeof auth>(),
    organizationClient(),
    twoFactorClient({
      onTwoFactorRedirect() {
        window.location.href = "/two-factor";
      }
    }),
    adminClient(),
    multiSessionClient()
  ],
  fetchOptions: {
    credentials: "include",
    onError(e: { error: { status: number; message?: string } }) {
      if (e.error.status === 429) {
        console.error("Too many requests. Please try again later.");
      }
    }
  }
});

export const {
  signUp,
  signIn,
  signOut,
  useSession,
  verifyEmail,
  sendVerificationEmail,
  forgetPassword,
  resetPassword,
  organization,
  useListOrganizations,
  useActiveOrganization,
  admin
} = authClient;

authClient.$store.listen("$sessionSignal", async () => {
  console.log("sessionSignal");
});
