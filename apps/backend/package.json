{"name": "backend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "@hono/node-server": "^1.15.0", "@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^0.19.9", "@repo/authentication": "workspace:*", "@repo/database": "workspace:*", "@repo/email": "workspace:*", "better-auth": "^1.2.12", "dotenv": "^17.1.0", "hono": "^4.8.4", "zod": "^3.25.76"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.15.3", "eslint": "^9.25.0", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "prettier": "@repo/prettier-config", "packageManager": "pnpm@9.15.4"}