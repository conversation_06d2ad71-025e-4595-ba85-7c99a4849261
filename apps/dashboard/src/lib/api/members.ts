import { apiDelete, apiGet, apiPatch } from "@/src/lib/config/api";

// Type definitions for API responses
export interface MemberUser {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  emailVerified: boolean;
  banned: boolean;
  banReason: string | null;
  banExpires: string | null;
}

export interface MemberInvitation {
  id: string;
  status: "pending" | "accepted" | "rejected" | "cancelled";
  expiresAt: string;
  createdAt: string;
}

export interface Member {
  id: string;
  role: "member" | "admin" | "owner";
  createdAt: string;
  user: MemberUser;
  invitation: MemberInvitation | null;
  status: "pending" | "active" | "suspended";
}

export interface MembersResponse {
  members: Member[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

export interface UpdateMemberRequest {
  role?: "member" | "admin" | "owner";
  ban?: boolean;
}

// Query parameters for fetching members
export interface FetchMembersParams {
  page?: number;
  pageSize?: number;
  status?: "pending" | "active" | "suspended";
  search?: string;
}

// Fetch organization members
export async function fetchMembers(
  organizationSlug: string,
  params: FetchMembersParams = {}
): Promise<MembersResponse> {
  const searchParams = new URLSearchParams();

  if (params.page !== undefined) {
    searchParams.set("page", params.page.toString());
  }

  if (params.pageSize !== undefined) {
    searchParams.set("pageSize", params.pageSize.toString());
  }

  if (params.status) {
    searchParams.set("status", params.status);
  }

  if (params.search) {
    searchParams.set("search", params.search);
  }

  const queryString = searchParams.toString();
  const endpoint = `/api/v1/organizations/${organizationSlug}/members${queryString ? `?${queryString}` : ""}`;

  const response = await apiGet(endpoint);

  if (!response.ok) {
    throw new Error(`Failed to fetch members: ${response.status}`);
  }

  const data = await response.json();

  return data;
}

// Update member role or suspension status
export async function updateMember(
  organizationSlug: string,
  memberId: string,
  data: UpdateMemberRequest
): Promise<Member> {
  const response = await apiPatch(
    `/api/v1/organizations/${organizationSlug}/members/${memberId}`,
    data
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error || `Failed to update member: ${response.status}`
    );
  }

  return response.json();
}

// Remove member from organization or cancel invitation
export async function removeMember(
  organizationSlug: string,
  memberId: string
): Promise<void> {
  const response = await apiDelete(
    `/api/v1/organizations/${organizationSlug}/members/${memberId}`
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error || `Failed to remove member: ${response.status}`
    );
  }
}

// Transfer organization ownership
export async function transferOwnership(
  organizationSlug: string,
  newOwnerId: string
): Promise<{
  message: string;
  newOwner: Member;
  formerOwner: Member;
}> {
  const response = await fetch(
    `/api/v1/organizations/${organizationSlug}/members/${newOwnerId}/transfer-ownership`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    }
  );

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(
      errorData.error || `Failed to transfer ownership: ${response.status}`
    );
  }

  return response.json();
}
