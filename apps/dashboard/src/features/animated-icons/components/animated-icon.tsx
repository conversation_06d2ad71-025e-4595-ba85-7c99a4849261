import React, { useEffect, useRef } from "react";
import <PERSON>tie, { LottieRefCurrentProps } from "lottie-react";

interface AnimatedIconProps {
  animationData: unknown;
  isHovered: boolean;
}

export default function AnimatedIcon({
  animationData,
  isHovered
}: AnimatedIconProps) {
  const lottieRef = useRef<LottieRefCurrentProps>(null);

  useEffect(() => {
    if (lottieRef.current) {
      if (isHovered) {
        lottieRef.current.play();
      } else {
        lottieRef.current.stop();
      }
    }
  }, [isHovered]);

  return (
    <div style={{ width: "18px", height: "18px" }}>
      <Lottie
        lottieRef={lottieRef}
        animationData={animationData}
        loop={false}
        autoplay={false}
        style={{ width: "100%", height: "100%" }}
        rendererSettings={{
          preserveAspectRatio: "xMidYMid slice"
        }}
      />
    </div>
  );
}
