{"name": "<PERSON><PERSON><PERSON>", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:clean": "pnpm run kill-ports && pnpm run dev", "kill-ports": "lsof -ti:3000,3001 | xargs kill -9 2>/dev/null || echo 'No processes found on ports 3000,3001'", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "typecheck": "turbo typecheck", "db:generate": "turbo run db:generate", "db:studio-prod": "cd packages/database && pnpm db:studio-prod", "db:studio": "cd packages/database && pnpm db:studio", "deploy:staging": "./scripts/deploy-staging.sh", "deploy:production": "./scripts/deploy-production.sh", "prepare": "node -e \"if (process.env.CI !== 'true') { require('child_process').execSync('husky', {stdio: 'inherit'}) }\""}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "husky": "^9.1.7", "prettier": "^3.6.2", "turbo": "^2.5.4", "typescript": "5.8.3"}, "packageManager": "pnpm@9.15.4", "engines": {"node": ">=20"}}