"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";

import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { cn } from "@repo/ui/utils/cn";

import { useCreateTodo } from "../hooks/use-todos";
import { createTodoSchema, type CreateTodoFormData } from "../schemas/todo-schema";

interface CreateTodoFormProps {
  organizationId: string;
}

export function CreateTodoForm({ organizationId }: CreateTodoFormProps) {
  const createTodoMutation = useCreateTodo(organizationId);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CreateTodoFormData>({
    resolver: zodResolver(createTodoSchema)
  });

  const onSubmit = (data: CreateTodoFormData) => {
    createTodoMutation.mutate(data, {
      onSuccess: () => {
        reset();
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            {...register("text")}
            placeholder="Add a new todo..."
            className={cn(
              errors.text && "border-red-500"
            )}
            disabled={createTodoMutation.isPending}
          />
          {errors.text && (
            <p className="text-sm text-red-500 mt-1">{errors.text.message}</p>
          )}
        </div>
        <Button
          type="submit"
          disabled={createTodoMutation.isPending}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Todo
        </Button>
      </div>
    </form>
  );
}