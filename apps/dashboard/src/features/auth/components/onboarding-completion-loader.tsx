"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useActiveOrganization } from "@repo/authentication/auth-client";
import { Spinner } from "@repo/ui/components/spinner";
import { AnimatePresence, motion } from "motion/react";

const OnboardingCompletionLoader: React.FC = () => {
  const [currentStage, setCurrentStage] = useState<"creating" | "ready">(
    "creating"
  );
  const router = useRouter();
  const { data: activeOrganization } = useActiveOrganization();

  useEffect(() => {
    // Transition to "Getting ready" after 1.5 seconds
    const stageTimer = setTimeout(() => {
      setCurrentStage("ready");
    }, 1500);

    // Complete loading after 3 seconds and redirect
    const completeTimer = setTimeout(() => {
      if (activeOrganization?.slug) {
        router.push(`/${activeOrganization.slug}/home`);
      } else {
        router.push("/");
      }
    }, 3000);

    return () => {
      clearTimeout(stageTimer);
      clearTimeout(completeTimer);
    };
  }, [activeOrganization?.slug, router]);

  return (
    <div className="flex items-center space-x-3">
      <Spinner
        size="small"
        color="primary"
      />

      <div className="w-32">
        <AnimatePresence mode="wait">
          <motion.span
            key={currentStage}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="left-0 whitespace-nowrap"
          >
            {currentStage === "creating"
              ? "Creating account"
              : "Getting ready..."}
          </motion.span>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default OnboardingCompletionLoader;
