#!/bin/bash

# Deploy backend to staging environment
echo "🚀 Deploying backend to staging environment..."

# Ensure we're in the project root
if [ ! -f "fly.staging.toml" ]; then
    echo "❌ Error: fly.staging.toml not found. Make sure you're in the project root."
    exit 1
fi

# Deploy to staging
fly deploy --config fly.staging.toml

if [ $? -eq 0 ]; then
    echo "✅ Staging deployment successful!"
    echo "🌐 Staging URL: https://centaly-backend-staging.fly.dev"
    echo "🔍 Health check: curl https://centaly-backend-staging.fly.dev/health"
else
    echo "❌ Staging deployment failed!"
    exit 1
fi 