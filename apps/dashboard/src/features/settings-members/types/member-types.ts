// Re-export types from API layer for consistency
export type {
  Member,
  MemberUser,
  MemberInvitation,
  MembersResponse,
  UpdateMemberRequest,
  FetchMembersParams
} from "@/src/lib/api/members";

// Additional frontend-specific types
export type MemberStatus = "pending" | "active" | "suspended";
export type MemberRole = "member" | "admin" | "owner";

// Table column data type (compatible with existing AG Grid implementation)
export interface MemberTableItem {
  userId: string;
  id: string;
  name: string;
  email: string;
  role: MemberRole;
  // Raw data for status derivation
  suspendedAt?: Date | null;
  invitationStatus?: "pending" | "accepted" | "rejected" | "cancelled";
  isUserActive?: boolean;
  // Computed status
  status: MemberStatus;
}

// Filter state type
export interface MemberFilterState {
  searchText: string;
  selectedStatuses: MemberStatus[];
  hasActiveFilters: boolean;
}

// Member actions type
export interface MemberActions {
  canEdit: boolean;
  canSuspend: boolean;
  canDelete: boolean;
  canResendInvite: boolean;
  canMakeAdmin: boolean;
  canTransferOwnership: boolean;
}
