import { z } from "zod";

// Schema for creating a new todo
export const createTodoSchema = z.object({
  text: z
    .string()
    .min(1, "Todo text is required")
    .max(500, "Todo text must be less than 500 characters")
    .trim()
});

// Schema for updating a todo
export const updateTodoSchema = z.object({
  text: z
    .string()
    .min(1, "Todo text is required")
    .max(500, "Todo text must be less than 500 characters")
    .trim()
    .optional(),
  completed: z.boolean().optional()
});

// Infer types from schemas
export type CreateTodoFormData = z.infer<typeof createTodoSchema>;
export type UpdateTodoFormData = z.infer<typeof updateTodoSchema>;