"use client";

import { createContext, ReactNode, useContext } from "react";

import type { Organization, Session } from "../dal/authentication";

/**
 * Context for server-validated workspace data
 * This context provides workspace data that was validated server-side during page load
 */
interface ValidatedWorkspaceContextType {
  slug: string;
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;

  // Helper methods for permissions (based on Organization structure)
  isCurrentWorkspace: (workspaceSlug: string) => boolean;
  canAccessWorkspace: (workspaceSlug: string) => boolean;
  getUserWorkspaces: () => Organization[];
}

const ValidatedWorkspaceContext = createContext<
  ValidatedWorkspaceContextType | undefined
>(undefined);

interface ValidatedWorkspaceProviderProps {
  children: ReactNode;
  slug: string;
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;
}

/**
 * Provider for server-validated workspace data
 * Use this when you have workspace data validated server-side in your page component
 */
export function ValidatedWorkspaceProvider({
  children,
  slug,
  session,
  organizations,
  currentWorkspace
}: ValidatedWorkspaceProviderProps) {
  const isCurrentWorkspace = (workspaceSlug: string) =>
    currentWorkspace.slug === workspaceSlug;

  const canAccessWorkspace = (workspaceSlug: string) =>
    organizations.some((org) => org.slug === workspaceSlug);

  const getUserWorkspaces = () => organizations;

  const value: ValidatedWorkspaceContextType = {
    slug,
    session,
    organizations,
    currentWorkspace,
    isCurrentWorkspace,
    canAccessWorkspace,
    getUserWorkspaces
  };

  return (
    <ValidatedWorkspaceContext.Provider value={value}>
      {children}
    </ValidatedWorkspaceContext.Provider>
  );
}

/**
 * Hook to access server-validated workspace data
 * This provides immediate access to validated workspace data without loading states
 */
export function useValidatedWorkspace() {
  const context = useContext(ValidatedWorkspaceContext);
  if (context === undefined) {
    throw new Error(
      "useValidatedWorkspace must be used within a ValidatedWorkspaceProvider"
    );
  }
  return context;
}

// Export types for use in other components
export type { ValidatedWorkspaceContextType };
