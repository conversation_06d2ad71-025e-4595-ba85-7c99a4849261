# fly.toml app configuration file for centaly-backend-staging
#
# Staging environment configuration with reduced resources
#

app = 'centaly-backend-staging'
primary_region = 'lhr'

[build]
  dockerfile = "Dockerfile"

[http_service]
  internal_port = 3001
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 1
  processes = ['app']

[[vm]]
  memory = '512mb'
  cpu_kind = 'shared'
  cpus = 1 