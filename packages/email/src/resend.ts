import { Resend } from "resend";

// Lazy initialization to avoid requiring RESEND_API_KEY at import time
function createResendInstance() {
  const apiKey = process.env.RESEND_API_KEY;
  if (!apiKey) {
    throw new Error(
      "RESEND_API_KEY is required. Please set it in your environment variables."
    );
  }
  return new Resend(apiKey);
}

let _resendInstance: Resend | null = null;

function getResend() {
  if (!_resendInstance) {
    _resendInstance = createResendInstance();
  }
  return _resendInstance;
}

export const resend = {
  emails: {
    send: (...args: Parameters<Resend['emails']['send']>) => {
      return getResend().emails.send(...args);
    }
  }
};
