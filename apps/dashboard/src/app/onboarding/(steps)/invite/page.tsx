import * as React from "react";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import InviteOnboardingForm from "@/src/features/auth/components/invite-onboarding-form";
import {
  getUserOrganizations,
  requireVerifiedUser
} from "@/src/features/auth/dal";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Invite Team | Centaly"
};

export default async function InviteOnboardingPage() {
  // Requires: authenticated + verified + has workspace
  const session = await requireVerifiedUser();
  const organizations = await getUserOrganizations();

  // If user has no workspace, redirect to workspace creation
  if (organizations.length === 0) {
    redirect("/onboarding/workspace");
  }

  // If user has completed onboarding, redirect to home
  if (session.user.onboardingStatus === "complete") {
    redirect("/");
  }

  return (
    <AuthContainer maxWidth="md">
      <InviteOnboardingForm />
    </AuthContainer>
  );
}
