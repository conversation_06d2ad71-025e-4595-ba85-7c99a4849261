"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import createAnimationData from "@/src/features/animated-icons/components/create-animated.json";
import searchAnimationData from "@/src/features/animated-icons/components/search-animated.json";
import { useWorkspace } from "@/src/features/auth/hooks/use-workspace";
import { Button } from "@repo/ui/components/button";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarRail
} from "@repo/ui/components/sidebar";
import { ArrowLeft, GalleryVerticalEnd } from "lucide-react";

import AnimatedIcon from "../../animated-icons/components/animated-icon";
import SidebarNavMain from "./sidebar-nav-main";
import SidebarNavSettings from "./sidebar-nav-settings";
import { WorkspaceSwitcher } from "./workspace-switcher";

const workspaces = [
  {
    name: "Acme Inc",
    logo: GalleryVerticalEnd,
    plan: "Enterprise"
  }
];

// Separate component for animated header buttons to prevent re-renders
const HeaderButtons = React.memo(() => {
  const [hoveredButton, setHoveredButton] = React.useState<string | null>(null);

  return (
    <div className="flex items-center gap-1">
      <Button
        className="hover:bg-neutral-200/70 rounded-full"
        size="iconXs"
        variant="ghost"
        onMouseEnter={() => setHoveredButton("create")}
        onMouseLeave={() => setHoveredButton(null)}
      >
        <AnimatedIcon
          animationData={createAnimationData}
          isHovered={hoveredButton === "create"}
        />
      </Button>
      <Button
        className="hover:bg-neutral-200/70 rounded-full"
        size="iconXs"
        variant="ghost"
        onMouseEnter={() => setHoveredButton("search")}
        onMouseLeave={() => setHoveredButton(null)}
      >
        <AnimatedIcon
          animationData={searchAnimationData}
          isHovered={hoveredButton === "search"}
        />
      </Button>
    </div>
  );
});

HeaderButtons.displayName = "HeaderButtons";

// Define navigation areas and their configurations
type NavigationArea = "default" | "settings";

interface AreaConfig {
  id: NavigationArea;
  direction: "left" | "right"; // Direction to slide in from
}

const navigationAreas: Record<NavigationArea, AreaConfig> = {
  default: {
    id: "default",
    direction: "left"
  },
  settings: {
    id: "settings",
    direction: "right"
  }
};

interface NavigationAreaProps {
  isActive: boolean;
  config: AreaConfig;
  children: React.ReactNode;
}

export function DashboardSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { slug } = useWorkspace();
  const pathname = usePathname();

  function NavigationArea({ isActive, config, children }: NavigationAreaProps) {
    const { direction } = config;

    return (
      <div
        className={`absolute inset-0 w-full transition-[opacity,transform] duration-300 ${
          isActive
            ? "opacity-100 transform-none pointer-events-auto"
            : `opacity-0 pointer-events-none ${
                direction === "left" ? "-translate-x-full" : "translate-x-full"
              }`
        }`}
      >
        {children}
      </div>
    );
  }

  // Determine the current navigation area based on the URL path
  const currentArea = React.useMemo((): NavigationArea => {
    return pathname.includes("/settings") ? "settings" : "default";
  }, [pathname]);

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              {currentArea === "settings" && (
                <Button
                  className="hover:bg-neutral-200/70 rounded-full"
                  size="iconXs"
                  variant="ghost"
                >
                  <Link
                    prefetch
                    href={slug ? `/${slug}/home` : "/home"}
                    className="cursor-pointer px-1"
                  >
                    <ArrowLeft />
                  </Link>
                </Button>
              )}
              <Link
                prefetch
                href={slug ? `/${slug}/home` : "/home"}
                className="cursor-pointer px-1"
              >
                <Image
                  src="/brand/centaly-logo.svg"
                  alt="Centaly Logo"
                  width={110}
                  height={30}
                  priority={true}
                />
              </Link>
            </div>
            <HeaderButtons />
          </div>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent className="relative overflow-x-hidden">
        {/* Default Navigation Area */}
        <NavigationArea
          isActive={currentArea === "default"}
          config={navigationAreas.default}
        >
          <div className="flex h-full flex-col">
            <SidebarNavMain />
          </div>
        </NavigationArea>

        {/* Settings Navigation Area */}
        <NavigationArea
          isActive={currentArea === "settings"}
          config={navigationAreas.settings}
        >
          <div className="flex h-full flex-col">
            <SidebarNavSettings />
          </div>
        </NavigationArea>
      </SidebarContent>
      <SidebarFooter>
        <WorkspaceSwitcher workspaces={workspaces} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
