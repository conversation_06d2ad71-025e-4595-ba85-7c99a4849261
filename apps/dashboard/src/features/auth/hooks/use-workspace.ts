"use client";

import { useParams } from "next/navigation";
import { apiGet } from "@/src/lib/config/api";
import { useQuery } from "@tanstack/react-query";

interface WorkspaceUser {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface Workspace {
  id: string;
  name: string;
  slug: string | null;
  logo: string | null;
  createdAt: string;
  metadata: string | null;
  userRole: "member" | "admin" | "owner";
}

interface WorkspaceError {
  error: string;
  timestamp: string;
  status?: number;
}

async function fetchWorkspace(slug: string): Promise<Workspace> {
  const response = await apiGet(`/api/v1/organizations/${slug}`);

  if (!response.ok) {
    const errorData: WorkspaceError = await response.json();
    const error = new Error(errorData.error) as Error & { status: number };
    error.status = response.status;
    throw error;
  }

  return response.json();
}

export function useWorkspace() {
  const params = useParams();
  const slug = params.slug as string;

  const {
    data: workspace,
    error,
    isLoading,
    isError
  } = useQuery({
    queryKey: ["workspace", slug],
    queryFn: () => fetchWorkspace(slug),
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on authentication or authorization errors
      if (error && typeof error === "object" && "status" in error) {
        const status = (error as { status: number }).status;
        if (status === 401 || status === 403 || status === 404) {
          return false;
        }
      }
      return failureCount < 3;
    }
  });

  return {
    workspace,
    loading: isLoading,
    error: error as Error & { status?: number },
    isError,
    slug
  };
}

export type { Workspace, WorkspaceUser, WorkspaceError };
