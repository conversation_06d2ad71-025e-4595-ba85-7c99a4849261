import { render } from "@react-email/render";
import {
  ConnectedAccountSecurityAlertEmail,
  type ConnectedAccountSecurityAlertEmailProps,
} from "../templates/connected-account-security-alert-email.js";
import { sendEmail } from "../resend/send-email.js";

export async function sendConnectedAccountSecurityAlertEmail(
  input: ConnectedAccountSecurityAlertEmailProps & { recipient: string }
): Promise<void> {
  const component = ConnectedAccountSecurityAlertEmail(input);
  const html = await render(component);
  const text = await render(component, { plainText: true });

  await sendEmail({
    recipient: input.recipient,
    subject: "Security Alert!",
    html,
    text,
  });
}
