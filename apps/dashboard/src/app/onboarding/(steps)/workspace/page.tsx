import * as React from "react";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import WorkspaceOnboardingForm from "@/src/features/auth/components/workspace-onboarding-form";
import {
  getUserOrganizations,
  requireVerifiedUser
} from "@/src/features/auth/dal";
import { AuthContainer } from "@repo/ui/components/auth-container";

export const metadata: Metadata = {
  title: "Create Workspace | Centaly"
};

export default async function WorkspaceOnboardingPage() {
  // Requires: authenticated + verified
  const session = await requireVerifiedUser();
  const organizations = await getUserOrganizations();

  // If user has completed onboarding, redirect to home
  if (session.user.onboardingStatus === "complete") {
    redirect("/");
  }

  // If user already has organizations, redirect them to home
  if (organizations.length > 0) {
    redirect("/");
  }
  return (
    <AuthContainer maxWidth="md">
      <WorkspaceOnboardingForm />
    </AuthContainer>
  );
}
