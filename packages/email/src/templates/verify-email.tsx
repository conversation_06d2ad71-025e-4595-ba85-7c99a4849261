import React from "react";
import {
  Body,
  Column,
  Container,
  Head,
  <PERSON><PERSON>,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text
} from "@react-email/components";

export interface VerifyEmailProps {
  email?: string;
  verifyLink?: string;
}

export const VerifyEmail = ({ email, verifyLink }: VerifyEmailProps) => {
  const previewText = `Verify your email address`;
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-[#FCFCFD] my-auto mx-auto font-sans py-[32px] px-6">
          <Container className="bg-white border border-solid border-[#E1E1E6] rounded-xl mx-auto py-9 px-10 max-w-[674px] mt-[32px] mb-[24px]">
            <Heading className="text-[#2C333B] text-[24px] font-normal text-left p-0 mb-[30px] mx-0">
              Verify your email
            </Heading>
            <Text className="text-[#2C333B] text-[15px] leading-[24px]">
              We need to verify your email address{" "}
              <span className="text-[#5753c6] ">{email}</span> before you can
              access your account. Click the link below to verify your email
              address.
            </Text>
            <Link
              href={verifyLink}
              className="text-[#5753c6] text-[16px] leading-[24px]"
            >
              Verify email address
            </Link>
            <Hr className="border border-solid border-[#E1E1E6] my-[32px] mx-0 w-full" />
            <Text className="text-[#63636c] text-[12px] leading-[18px]">
              This link expires in 10 minutes.
            </Text>
            <Text className="text-[#63636c] text-[12px] leading-[18px]">
              If you didn&apos;t sign up for Centaly, you can safely ignore this
              email. Someone else might have typed your email address by
              mistake.
            </Text>
          </Container>
          <Section className="text-center">
            <table className="w-full">
              <tr className="w-full">
                <td align="center">
                  <Link href="https://centaly.com">
                    <Img
                      alt="Centaly logo"
                      height="32"
                      src="https://app.centaly.com/brand/centaly-logo.svg"
                    />
                  </Link>
                </td>
              </tr>
              <tr>
                <td align="center">
                  <Row className="table-cell h-[42px] w-[56px] align-bottom">
                    <Column className="pr-[8px]">
                      <Link href="#">
                        <Img
                          alt="X"
                          height="24"
                          src="https://react.email/static/x-logo.png"
                          width="24"
                        />
                      </Link>
                    </Column>
                    <Column>
                      <Link href="#">
                        <Img
                          alt="Instagram"
                          height="24"
                          src="https://react.email/static/instagram-logo.png"
                          width="24"
                        />
                      </Link>
                    </Column>
                  </Row>
                </td>
              </tr>
              <tr>
                <td align="center">
                  <Text className="my-[8px] text-[12px] leading-[18px] text-[#63636c]">
                    Centaly HQ, Main Gate Rd, Kent ME4 4TZ
                  </Text>
                </td>
              </tr>
            </table>
          </Section>
        </Body>
      </Tailwind>
    </Html>
  );
};

export function reactVerifyEmail(props: VerifyEmailProps) {
  return <VerifyEmail {...props} />;
}
