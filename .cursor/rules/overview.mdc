---
alwaysApply: true
---

## Project Overview:

You are developing Centaly, Centaly is a software-as-a-service (SaaS) knowledge management platform designed for small and medium b2b organizations. It aims to centralize and standardize operational processes and knowledge, ensuring consistency, efficiency, control and quality as organizations scale.

## Project Structure

This is a Turborepo monorepo with the following structure:

- `apps/dashboard/` - Next.js application (main app)
- `apps/backend/` - Hono.js backend API server
- `apps/email/` - Email preview development server using React Email
- `packages/ui/` - Shared React component library using shadcn/ui
- `packages/database/` - Shared Prisma database package with MySQL (PlanetScale)
- `packages/authentication/` - Shared authentication package using Better-Auth
- `packages/email/` - Shared email package with React Email templates and Resend integration
- `packages/eslint-config/` - Shared ESLint configurations
- `packages/typescript-config/` - Shared TypeScript configurations
- `packages/prettier-config/` - Shared Prettier configuration

## Essential Commands

### Development

```bash
# Start development server for all apps
pnpm dev

# Start development with clean ports (kills existing processes)
pnpm dev:clean

# Kill processes on development ports
pnpm kill-ports

# Start development for specific app
pnpm dev --filter=dashboard
pnpm dev --filter=backend
pnpm dev --filter=email

# Start UI component development
cd packages/ui && pnpm dev:components && pnpm dev:styles
```

### Building

```bash
# Build all packages and apps
pnpm build

# Build specific app
pnpm build --filter=dashboard
```

### Code Quality

```bash
# Lint all packages
pnpm lint

# Type check all packages
pnpm typecheck

# Format code
pnpm format
```

### Database

```bash
# Generate Prisma client
pnpm db:generate

# Open Prisma Studio (development)
pnpm db:studio

# Open Prisma Studio (production)
pnpm db:studio-prod

# Run database migrations (from database package)
cd packages/database && pnpm db:deploy

# Seed database (from database package)
cd packages/database && pnpm db:seed

# Push schema changes (dev only, from database package)
cd packages/database && pnpm db:push
```

## Architecture Notes

### UI Package (`packages/ui/`)

- Uses shadcn/ui components with Tailwind CSS v4
- Components are exported from `src/components/`
- Utilities (like `cn` function) are in `src/utils/`
- Global styles are in `src/styles/globals.css`
- Component generation: `pnpm generate:component`

### Database Package (`packages/database/`)

- Prisma ORM with MySQL (PlanetScale)
- Shared database client and types
- Migrations managed via Prisma
- Exports client from `@repo/database`
- Prisma Accelerate extension for connection pooling

### Authentication Package (`packages/authentication/`)

- Better-Auth library for authentication
- Exports auth server config from `@repo/authentication/auth`
- Exports auth client utilities from `@repo/authentication/auth-client`
- Integrates with the database package for user management

### Dashboard App (`apps/dashboard/`)

- Next.js 15 with React 19
- Uses Turbopack for development
- Imports UI components from `@repo/ui`
- Tailwind CSS v4 with PostCSS

### backend App (`apps/backend/`)

- Hono.js backend server
- OpenAPI/Swagger documentation
- Type-safe environment variables with Zod
- Error handling and request logging middleware
- CORS configured for dashboard app
- Authentication integration with Better-Auth

### Email App (`apps/email/`)

- React Email preview development server
- Runs on port 3003 for email template development
- Uses templates from `@repo/email` package
- Email export functionality for static HTML

### Email Package (`packages/email/`)

- React Email templates and components
- Resend integration for email sending
- Exports templates from `@repo/email/templates`
- Exports email senders from `@repo/email/senders`
- Exports Resend configuration from `@repo/email/resend`
- TypeScript definitions for email sending

### Prettier Config Package (`packages/prettier-config/`)

- Shared Prettier configuration
- Import sorting with `@ianvs/prettier-plugin-sort-imports`
- Tailwind CSS class sorting with `prettier-plugin-tailwindcss`
- Exported as `@repo/prettier-config`

### Shared Configurations

- ESLint: Shared configs for base, Next.js, and React
- TypeScript: Shared tsconfig.json files
- Tailwind: Shared configuration and styles
- Prettier: Shared formatting configuration

## Development Workflow

1. The monorepo uses pnpm workspaces
2. All packages are linked via `workspace:*` protocol
3. Turbo handles task orchestration and caching
4. TypeScript is used throughout with strict type checking
5. ESLint enforces code quality with max-warnings 0

## Package Dependencies

The UI package uses:

- Radix UI primitives
- Class Variance Authority for component variants
- Tailwind Merge and clsx for className utilities
- Tailwind CSS v4 with CLI

The Database package uses:

- Prisma ORM for database operations
- MySQL (PlanetScale) as the database
- Prisma Accelerate for connection pooling
- TypeScript for type safety

The Authentication package uses:

- Better-Auth library for authentication flows
- React integration for client-side auth
- Database package integration for user data
- TypeScript for type safety

The Email package uses:

- React Email for email template development
- Resend for email delivery service
- Tailwind CSS for email styling
- Zod for type validation
- TypeScript for type safety

The Prettier Config package uses:

- Prettier for code formatting
- Import sorting plugin for organized imports
- Tailwind CSS plugin for class sorting
- Shared configuration across monorepo

## Node.js and Package Manager Requirements

- Node.js >= 20
- pnpm 9.15.4 (specified in packageManager field)
- TypeScript 5.8.x across all packages

### Manual Quality Checks

Run these commands before committing:

```bash
# Run all quality checks
pnpm lint && pnpm typecheck

# Format code
pnpm format
```
