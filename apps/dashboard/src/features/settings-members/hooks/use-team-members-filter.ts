import { useCallback, useState } from "react";
import type { AgGridReact } from "ag-grid-react";

export interface UseTeamMembersFilterReturn {
  searchText: string;
  setSearchText: (text: string) => void;
  clearSearch: () => void;
  applyQuickFilter: (gridRef: React.RefObject<AgGridReact | null>) => void;
  selectedStatuses: string[];
  setSelectedStatuses: (statuses: string[]) => void;
  toggleStatus: (status: string) => void;
  clearStatusFilter: () => void;
  hasActiveFilters: boolean;
}

/**
 * Custom hook for managing team members table search/filter functionality
 * Uses AG-Grid's Quick Filter and External Filter features
 */
export function useTeamMembersFilter(): UseTeamMembersFilterReturn {
  const [searchText, setSearchText] = useState<string>("");
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);

  /**
   * Apply the quick filter to the AG-Grid instance
   */
  const applyQuickFilter = useCallback(
    (gridRef: React.RefObject<AgGridReact | null>) => {
      if (gridRef.current?.api) {
        gridRef.current.api.setGridOption("quickFilterText", searchText);
      }
    },
    [searchText]
  );

  /**
   * Clear the search text and filter
   */
  const clearSearch = useCallback(() => {
    setSearchText("");
  }, []);

  /**
   * Toggle a status in the selected statuses array
   */
  const toggleStatus = useCallback((status: string) => {
    setSelectedStatuses((prev) => {
      if (prev.includes(status)) {
        return prev.filter((s) => s !== status);
      } else {
        return [...prev, status];
      }
    });
  }, []);

  /**
   * Clear all status filters
   */
  const clearStatusFilter = useCallback(() => {
    setSelectedStatuses([]);
  }, []);

  /**
   * Check if there are any active filters
   */
  const hasActiveFilters = Boolean(searchText || selectedStatuses.length > 0);

  return {
    searchText,
    setSearchText,
    clearSearch,
    applyQuickFilter,
    selectedStatuses,
    setSelectedStatuses,
    toggleStatus,
    clearStatusFilter,
    hasActiveFilters
  };
}
