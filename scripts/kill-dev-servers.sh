#!/bin/bash

echo "🔍 Checking for processes on development ports..."

# Function to kill processes on a specific port
kill_port() {
    local port=$1
    local pids=$(lsof -ti:$port 2>/dev/null)
    
    if [ -n "$pids" ]; then
        echo "🛑 Killing processes on port $port: $pids"
        echo "$pids" | xargs kill -9 2>/dev/null
        echo "✅ Processes on port $port killed"
    else
        echo "✅ No processes found on port $port"
    fi
}

# Kill processes on ports 3000 and 3001
kill_port 3000
kill_port 3001

echo "🎉 Development server cleanup complete!" 