import { config } from "dotenv";
import { z } from "zod";

// Load environment variables from .env file
config();

// Define environment schema
const envSchema = z.object({
  NODE_ENV: z
    .enum(["development", "production", "test"])
    .default("development"),
  PORT: z.coerce.number().int().positive().default(3001),
  DATABASE_URL: z.string().url().min(1),
  BETTER_AUTH_SECRET: z.string(),
  BETTER_AUTH_URL: z.string().url().default("http://localhost:3001"),
  FRONTEND_URL: z.string().url().optional(),
  LOG_LEVEL: z.enum(["debug", "info", "warn", "error"]).default("info"),
  EMAIL_FROM: z.string().min(1),
  RESEND_API_KEY: z.string().min(1)
});

// Parse and validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse({
      NODE_ENV: process.env.NODE_ENV,
      PORT: process.env.PORT,
      DATABASE_URL: process.env.DATABASE_URL,
      BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET,
      BETTER_AUTH_URL: process.env.BETTER_AUTH_URL,
      FRONTEND_URL: process.env.FRONTEND_URL,
      LOG_LEVEL: process.env.LOG_LEVEL,
      EMAIL_FROM: process.env.EMAIL_FROM,
      RESEND_API_KEY: process.env.RESEND_API_KEY
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("❌ Invalid environment variables:");
      console.error(error.format());
      process.exit(1);
    }
    throw error;
  }
};

// Export validated environment variables
export const env = parseEnv();

// Export type for use in other files
export type Env = z.infer<typeof envSchema>;

// Helper to check if we're in development
export const isDevelopment = env.NODE_ENV === "development";
export const isProduction = env.NODE_ENV === "production";
export const isTest = env.NODE_ENV === "test";
