import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2Icon } from "lucide-react";

import { cn } from "../utils/cn";

const spinnerVariants = cva("flex-col items-center justify-center", {
  variants: {
    show: {
      true: "flex",
      false: "hidden"
    }
  },
  defaultVariants: {
    show: true
  }
});

const loaderVariants = cva("animate-spin", {
  variants: {
    size: {
      small: "size-6 shrink-0",
      medium: "size-8 shrink-0",
      large: "size-12 shrink-0"
    },
    color: {
      neutral: "text-neutral-600",
      primary: "text-primary-violet"
    }
  },
  defaultVariants: {
    size: "medium",
    color: "neutral"
  }
});

export type SpinnerProps = VariantProps<typeof spinnerVariants> &
  VariantProps<typeof loaderVariants> & {
    className?: string;
    children?: React.ReactNode;
  };
function Spinner({
  size,
  color,
  show,
  children,
  className
}: SpinnerProps): React.JSX.Element {
  return (
    <span className={spinnerVariants({ show })}>
      <Loader2Icon className={cn(loaderVariants({ size, color }), className)} />
      {children}
    </span>
  );
}

export type CenteredSpinnerProps = SpinnerProps & {
  containerClassName?: React.HTMLAttributes<HTMLDivElement>["className"];
};
function CenteredSpinner({
  containerClassName,
  ...props
}: CenteredSpinnerProps): React.JSX.Element {
  return (
    <div
      className={cn(
        "pointer-events-none absolute inset-0 flex select-none items-center justify-center opacity-65",
        containerClassName
      )}
    >
      <Spinner {...props} />
    </div>
  );
}
CenteredSpinner.displayName = "CenteredSpinner";

export { CenteredSpinner, Spinner };
