---
description:
globs:
alwaysApply: false
---

## Goal

You are tasked with creating a well-structured PRD in Markdown format for feature requests, bug reports or improvement ideas. Your goal is to turn the provided feature description into a comprehensive PRD that follows best practices and project conventions. The PRD should be clear, actionable, and suitable for a junior developer to understand and implement the feature.

## Process

1. Receive Initial Prompt: The user provides a brief description or request for a new feature or functionality.

2. Ask Clarifying Questions: Before writing the PRD, the AI must ask clarifying questions to gather sufficient detail. The goal is to understand the "what" and "why" of the feature, not necessarily the "how" (which the developer will figure out). Make sure to provide options in letter/number lists so I can respond easily with my selections.

3. Generate PRD: Based on the initial prompt and the user's answers to the clarifying questions, generate a PRD using the structure outlined below.

4. Save PRD: Save the generated document as prd-[feature-name].md inside the /docs/PRD directory.

## Clarifying Questions (Examples):

Adapt your questions based on the prompt, but here are some common areas to explore. Group questions thematically to reduce overwhelm, and limit to 5-8 questions per interaction for iterative refinement:

- **Problem/Goal**: "What problem does this feature solve for the user?" or "What is the main goal we want to achieve with this feature?"
- **Target User**: "Who is the primary user of this feature?"
- **Core Functionality**: "Can you describe the key actions a user should be able to perform with this feature?" Are there technical constraints, such as platform limitations or required integrations?
- **User Stories**: "Could you provide a few user stories? (e.g., As a [type of user], I want to [perform an action] so that [benefit].)"
- **Documentation**: Internal or external documentation that could be used to refer to.
- **Acceptance Criteria**: "How will we know when this feature is successfully implemented? What are the key success criteria?"
- **Scope/Boundaries**: "Are there any specific things this feature should not do (non-goals)?"
- **Data Requirements**: "What kind of data does this feature need to display or manipulate?"
- **Design/UI**: "Are there any existing design mockups or UI guidelines to follow?" or "Can you describe the desired look and feel?"
- **Edge Cases**: "Are there any potential edge cases or error conditions we should consider?"

## PRD Structure

The generated PRD should include the following sections:

1. Overview/Introduction: [Provide a high-level summary of the PRD, including the purpose of the feature, its goals (using SMART criteria: Specific, Measurable, Achievable, Relevant, Time-bound), and target personas. This section sets the context for the reader.]

2. Problem Statement: [Describe the core issues with the current system or user experience. Use a numbered or bulleted list for clarity. Include: Key pain points (e.g., instability, redundancy, surprises). How users express these problems (e.g., indirect feature requests like custom fields or more categories). Support with evidence from user feedback, data, or observations.].

3. Solution Vision: [Outline the proposed approach in clear, straightforward terms. Explain how it addresses the problems directly. If relevant, describe mechanics like UI interactions (e.g., drag-and-drop) and any integrations with existing features. Emphasize why this is the best fit (e.g., simplicity, alignment with product philosophy).]

4. User Stories: [List user stories in the standard format: "As a [type of user], I want [an action] so that [a benefit]." Include 3-5 stories that cover key scenarios, emphasizing benefits like reduced complexity or mental load. Describe real-world examples of how users will benefit.]

5. Functional Requirements: List the specific functionalities the feature must have. Use clear, concise language (e.g., "The system must allow users to upload a profile picture."). Be as specific as possible in describing the feature's behaviour and functionality. Number these requirements (e.g. FR001)

## Target Audience

Assume the primary reader of the PRD is a junior developer. Therefore, requirements should be explicit, unambiguous, and avoid jargon where possible. Provide enough detail for them to understand the feature's purpose and core logic.

## Output

Format: Markdown (.md)
Location: /docs/PRD/
Filename: prd-[feature-name].md

## Final instructions

Do NOT start implementing the PRD
Make sure to ask the user clarifying questions
Take the user's answers to the clarifying questions and improve the PRD
