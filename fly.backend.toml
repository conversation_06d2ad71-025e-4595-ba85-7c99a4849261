# fly.toml app configuration file generated for centaly-backend on 2025-07-15T17:29:00+01:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'centaly-backend'
primary_region = 'lhr'

[build]
  dockerfile = "Dockerfile"

[http_service]
  internal_port = 3001
  force_https = true
  auto_stop_machines = "off"
  auto_start_machines = false
  min_machines_running = 3
  processes = ['app']

[[vm]]
  memory = '8gb'
  cpu_kind = 'shared'
  cpus = 4 