# Team Member Management Feature

## Overview

The Team Member Management feature enables workspace administrators to invite new members, view all team members (active, suspended, and invited), and manage their access levels within the organization. This feature integrates with Better-Auth's organization plugin to provide comprehensive member management capabilities including invitation workflows, status tracking, and role-based access control.

Main Objectives:
Enable workspace managers to invite new members via email with role assignment
Display a comprehensive table of all members with filtering and search capabilities
Track member status (active, suspended, invited) across multiple data sources
Provide resend invitation functionality for pending invitees
Provide seamless integration with existing Better-Auth organization functionality

## Implementation Approach

Three-Layer Architecture:

1. Backend API Layer: Create organization member management endpoints
2. DAL (Data Access Layer): Server-side data fetching with caching
3. Client Layer: React Query hooks for client-side state management

Data Aggregation Strategy:
The deriveMemberStatus function requires combining data from three sources:

- Member table: suspendedAt, role fields
- Invitation table: status field
- User table: name, email, emailVerified fields

## 1. Architecture Overview

### System Design

The Team Member Management feature follows a **three-layer architecture** that separates concerns and promotes maintainability:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
├─────────────────────────────────────────────────────────────┤
│  React Components (settings-members/)                       │
│  • team-members-table.tsx (AG Grid table)                   │
│  • team-member-invite-modal.tsx (invitation form)           │
│  • team-members-table-filter.tsx (search & filters)         │
├─────────────────────────────────────────────────────────────┤
│  State Management & Data Layer                              │
│  • hooks/ (React Query + custom hooks)                      │
│  • dal/ (Server-side data access)                           │
│  • types/ & schemas/ (Type definitions)                     │
├─────────────────────────────────────────────────────────────┤
│                    Backend Layer                            │
│  • /api/v1/organizations/{slug}/members (REST API)          │
│  • Prisma ORM + PlanetScale MySQL                           │
│  • Better-Auth integration for invitations                  │
└─────────────────────────────────────────────────────────────┘
```

### File Hierarchy

```
apps/dashboard/src/features/settings-members/
├── dal/
│   └── members.ts                    # Server-side data fetching (SSR)
├── hooks/
│   ├── use-members.ts               # React Query hooks & mutations
│   └── use-team-members-filter.ts   # AG Grid filtering logic
├── schemas/
│   └── member-schema.ts             # Zod validation schemas
├── types/
│   └── member-types.ts              # TypeScript type definitions
├── team-member-invite-modal.tsx     # Invitation modal component
├── team-members-table-filter.tsx    # Search & filter controls
└── team-members-table.tsx           # Main AG Grid table component

apps/backend/src/routes/
└── members.ts                       # REST API endpoints

apps/dashboard/src/lib/api/
└── members.ts                       # Client-side API functions
```

## 2. Key Components

### Core UI Components

**`team-members-table.tsx`** - Main data table component

- **Responsibility**: Displays members in AG Grid with custom cell renderers
- **Key Features**: Status badges, role management, action dropdowns, pagination, resend invitation functionality
- **Dependencies**: AG Grid Community, custom hooks, UI components
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-members-table.tsx:199`

**`team-member-invite-modal.tsx`** - Invitation dialog

- **Responsibility**: Handles member invitation with email tags and role selection
- **Key Features**: Multi-email input, role selection, Better-Auth integration
- **Dependencies**: React Hook Form, Zod validation, Better-Auth client
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-member-invite-modal.tsx:48`

**`team-members-table-filter.tsx`** - Search and filter controls

- **Responsibility**: Provides search input and status filtering popover
- **Key Features**: Real-time search, multi-select status filters, filter state management
- **Dependencies**: Custom filter hook, UI components
- **Line Reference**: `apps/dashboard/src/features/settings-members/team-members-table-filter.tsx:36`

### Data Management Layer

**`dal/members.ts`** - Server-side data access

- **Responsibility**: Cached server-side data fetching for SSR/SSG
- **Key Features**: React cache(), headers forwarding, error handling
- **Dependencies**: Next.js headers, fetch API
- **Line Reference**: `apps/dashboard/src/features/settings-members/dal/members.ts:10`

**`hooks/use-members.ts`** - Client-side state management

- **Responsibility**: React Query hooks with optimistic updates
- **Key Features**: Data fetching, mutations, optimistic UI updates, cache invalidation, resend invitation functionality
- **Dependencies**: TanStack Query, API functions, toast notifications
- **Line Reference**: `apps/dashboard/src/features/settings-members/hooks/use-members.ts:16`

**`hooks/use-team-members-filter.ts`** - Filter state management

- **Responsibility**: Manages AG Grid quick filter and external filter state
- **Key Features**: Search text management, status filtering, grid integration
- **Dependencies**: AG Grid React, React hooks
- **Line Reference**: `apps/dashboard/src/features/settings-members/hooks/use-team-members-filter.ts:20`

### Backend API

**`apps/backend/src/routes/members.ts`** - REST API endpoints

- **Responsibility**: CRUD operations for organization members
- **Key Features**: Member listing with aggregation, role updates, suspension, removal
- **Dependencies**: Hono.js, Prisma ORM, OpenAPI integration
- **Line Reference**: `apps/backend/src/routes/members.ts:104`

**`apps/dashboard/src/lib/api/members.ts`** - API client functions

- **Responsibility**: Type-safe API calls from frontend to backend
- **Key Features**: Standardized error handling, TypeScript interfaces
- **Dependencies**: Custom API configuration, fetch wrapper
- **Line Reference**: `apps/dashboard/src/lib/api/members.ts:54`

### Authentication & Email Integration

**`packages/authentication/src/lib/auth.ts`** - Better-Auth configuration

- **Responsibility**: Configures Better-Auth with organization plugin and email sending
- **Key Features**: Organization plugin setup, email invitation handling, Resend integration
- **Email Template**: Uses `@repo/email/invite-user-email` React Email template
- **Dependencies**: Better-Auth, Resend, React Email templates
- **Line Reference**: `packages/authentication/src/lib/auth.ts:159`

## 3. Data Flow

### Member Data Aggregation

```mermaid
graph TD
    A[Frontend Request] --> B[Backend API]
    B --> C[Prisma Query: Members Table]
    B --> D[Prisma Query: Invitations Table]
    B --> E[Prisma Query: Users Table]
    C --> F[Data Aggregation Layer]
    D --> F
    E --> F
    F --> G[deriveMemberStatus Function]
    G --> H[Formatted Response]
    H --> I[React Query Cache]
    I --> J[AG Grid Table]
```

### Status Derivation Logic

The `deriveMemberStatus` function (apps/backend/src/routes/members.ts:242) combines data from three sources:

1. **Priority 1 - Suspension**: If `member.suspendedAt` exists → `"suspended"`
2. **Priority 2 - Pending Invitation**: If `invitation.status === "pending"` → `"pending"`
3. **Priority 3 - Active User**: If invitation accepted + email verified → `"active"`
4. **Default**: Legacy members without invitations → `"active"`

### Invitation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant M as Modal
    participant BA as Better-Auth
    participant AUTH as Auth Package
    participant EMAIL as Email Package
    participant DB as Database
    participant API as Backend API

    U->>M: Enter emails & role
    M->>BA: authClient.organization.inviteMember()
    BA->>DB: Create invitation record
    BA->>AUTH: Trigger sendInvitationEmail hook
    AUTH->>EMAIL: Generate invitation email template
    EMAIL->>AUTH: Return React Email component
    AUTH->>EMAIL: Send via Resend API
    EMAIL-->>U: Email delivered to invitee
    BA->>M: Return invitation result
    M->>API: Trigger cache invalidation
    API->>U: Update table with new pending member
```

### Resend Invitation Flow

```mermaid
sequenceDiagram
    participant U as User
    participant T as Table
    participant BA as Better-Auth
    participant AUTH as Auth Package
    participant EMAIL as Email Package
    participant API as Backend API

    U->>T: Click "Resend invite" dropdown action
    T->>BA: authClient.organization.inviteMember({ resend: true })
    BA->>AUTH: Trigger sendInvitationEmail hook (resend)
    AUTH->>EMAIL: Generate invitation email template
    EMAIL->>AUTH: Return React Email component
    AUTH->>EMAIL: Send via Resend API
    EMAIL-->>U: Email re-delivered to invitee
    BA->>T: Return success/error response
    T->>API: Trigger cache invalidation
    API->>U: Show success/error toast notification
```

### Data Update Patterns

- **Optimistic Updates**: Immediate UI updates before server confirmation
- **Cache Invalidation**: React Query invalidates affected queries after mutations
- **Error Rollback**: Failed mutations revert optimistic changes
- **Real-time Sync**: Status changes reflect immediately in the table

## 4. Patterns

### State Management Architecture

- **React Query**: Server state management with caching and synchronization
- **Local State**: Component-level state for UI interactions (modals, filters)
- **Form State**: React Hook Form with Zod validation for data integrity
- **Global State**: Better-Auth context for authentication state

### Component Composition

- **Container/Presentational Pattern**: Hooks handle logic, components handle rendering
- **Custom Hooks Pattern**: Reusable logic encapsulated in custom hooks
- **Render Props Pattern**: AG Grid cell renderers for customized table cells
- **Compound Components**: Modal components with sub-components for structure

### API Integration Patterns

- **Server-Side Rendering**: DAL functions provide initial data for SSR
- **Client-Side Hydration**: React Query manages subsequent data fetching
- **Type-Safe API Calls**: Shared TypeScript interfaces between frontend/backend
- **Error Boundary Pattern**: Consistent error handling across API layers

### Authentication Integration

- **Better-Auth Plugin**: Organization plugin handles member invitations
- **Session Management**: Server-side session validation for API access
- **Role-Based Access**: Permission checks at both API and UI levels
- **Optimistic Auth Updates**: Immediate UI feedback for auth-related actions

### Resend Email Invitation

- **Better-Auth**: Uses `authClient.organization.inviteMember()` with `resend: true` parameter
- **User Feedback**: Toast notifications for loading, success, and error states
- **State Management**: React Query mutation hooks for optimistic updates and cache invalidation
- **Error Handling**: Comprehensive error handling with user-friendly messages

### Email Integration Architecture

- **Better-Auth Hook**: `sendInvitationEmail` hook automatically triggered on invitation creation
- **React Email Templates**: Uses `@repo/email/invite-user-email` for consistent branding
- **Resend Integration**: Configured in auth package for reliable email delivery
- **Environment-Aware Links**: Dynamic invitation links based on development/production environment
- **Error Handling**: Email failures are caught and logged without blocking invitation creation

### Performance Optimizations

- **AG Grid Virtualization**: Efficient rendering of large member lists
- **React Query Caching**: Reduces redundant API calls
- **Optimistic Updates**: Immediate UI feedback without waiting for server
- **Server-Side Caching**: React cache() reduces server-side computation

## Rules to adhere to

- crud-operations.mdc
- api-usage-guide.mdc

## Reference Documentation

https://www.better-auth.com/docs/plugins/organization
