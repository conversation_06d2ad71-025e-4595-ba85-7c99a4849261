import { cache } from "react";
import { headers } from "next/headers";

// Todo types
export interface Todo {
  id: number;
  text: string;
  completed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTodoData {
  text: string;
}

export interface UpdateTodoData {
  text?: string;
  completed?: boolean;
}

interface TodosResponse {
  todos: Todo[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
}

// Get todos for organization with caching
export const getTodos = cache(async (
  organizationId: string,
  options?: {
    page?: number;
    pageSize?: number;
    completed?: boolean;
  }
): Promise<TodosResponse> => {
  try {
    const headersList = await headers();
    
    const searchParams = new URLSearchParams();
    if (options?.page) searchParams.set("page", options.page.toString());
    if (options?.pageSize) searchParams.set("pageSize", options.pageSize.toString());
    if (options?.completed !== undefined) searchParams.set("completed", options.completed.toString());

    const url = `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos${searchParams.toString() ? `?${searchParams.toString()}` : ""}`;

    const response = await fetch(url, {
      headers: {
        Cookie: headersList.get("cookie") || "",
        "Content-Type": "application/json"
      },
      cache: "no-store",
      credentials: "include"
    });

    if (!response.ok) {
      console.error(`Failed to fetch todos: ${response.status} ${response.statusText}`);
      return {
        todos: [],
        pagination: {
          page: options?.page || 1,
          pageSize: options?.pageSize || 20,
          total: 0,
          totalPages: 0
        }
      };
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to fetch todos:", error);
    return {
      todos: [],
      pagination: {
        page: options?.page || 1,
        pageSize: options?.pageSize || 20,
        total: 0,
        totalPages: 0
      }
    };
  }
});

// Get single todo by ID
export const getTodo = cache(async (
  organizationId: string,
  todoId: number
): Promise<Todo | null> => {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos/${todoId}`,
      {
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        cache: "no-store",
        credentials: "include"
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      console.error(`Failed to fetch todo: ${response.status} ${response.statusText}`);
      return null;
    }

    const todo = await response.json();
    return todo;
  } catch (error) {
    console.error("Failed to fetch todo:", error);
    return null;
  }
});

// Create new todo
export async function createTodo(
  organizationId: string,
  data: CreateTodoData
): Promise<Todo | null> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos`,
      {
        method: "POST",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data),
        credentials: "include"
      }
    );

    if (!response.ok) {
      console.error(`Failed to create todo: ${response.status} ${response.statusText}`);
      return null;
    }

    const todo = await response.json();
    return todo;
  } catch (error) {
    console.error("Failed to create todo:", error);
    return null;
  }
}

// Update todo
export async function updateTodo(
  organizationId: string,
  todoId: number,
  data: UpdateTodoData
): Promise<Todo | null> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos/${todoId}`,
      {
        method: "PATCH",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data),
        credentials: "include"
      }
    );

    if (!response.ok) {
      console.error(`Failed to update todo: ${response.status} ${response.statusText}`);
      return null;
    }

    const todo = await response.json();
    return todo;
  } catch (error) {
    console.error("Failed to update todo:", error);
    return null;
  }
}

// Delete todo
export async function deleteTodo(
  organizationId: string,
  todoId: number
): Promise<boolean> {
  try {
    const headersList = await headers();

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationId}/todos/${todoId}`,
      {
        method: "DELETE",
        headers: {
          Cookie: headersList.get("cookie") || "",
          "Content-Type": "application/json"
        },
        credentials: "include"
      }
    );

    if (!response.ok) {
      console.error(`Failed to delete todo: ${response.status} ${response.statusText}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Failed to delete todo:", error);
    return false;
  }
}