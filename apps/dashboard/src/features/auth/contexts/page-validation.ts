import { Organization, requireWorkspaceAccess, Session } from "../dal";

/**
 * Utility interface for validated page props with workspace data
 */
export interface ValidatedWorkspacePageProps {
  slug: string;
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;
}

/**
 * Minimal workspace validation that only ensures access without returning data
 * Use this when you only need to verify access but don't need workspace data
 *
 * @param slug - The workspace slug from params
 */
export async function validateWorkspaceAccess(slug: string): Promise<void> {
  // This will throw/redirect if user doesn't have access
  await requireWorkspaceAccess(slug);
}

/**
 * Utility function to validate workspace access and return validated data
 * Use this in page components that need workspace validation and data
 *
 * @param slug - The workspace slug from params
 * @returns Validated workspace data including session, organizations, and current workspace
 */
export async function validateWorkspacePage(
  slug: string
): Promise<ValidatedWorkspacePageProps> {
  const { session, organizations, currentWorkspace } =
    await requireWorkspaceAccess(slug);

  return {
    slug,
    session,
    organizations,
    currentWorkspace
  };
}

/**
 * Type-safe workspace page component props
 */
export interface WorkspacePageProps {
  params: Promise<{ slug: string }>;
}

/**
 * Higher-order function for creating workspace-validated page components
 * This provides a consistent pattern for pages that need workspace validation
 *
 * @example
 * ```tsx
 * export default createWorkspacePage(async ({ slug, currentWorkspace, session }) => {
 *   return (
 *     <div>
 *       <h1>Welcome to {currentWorkspace.name}</h1>
 *       <p>Hello, {session.user.name}</p>
 *     </div>
 *   );
 * });
 * ```
 */
export function createWorkspacePage<T extends Record<string, unknown> = {}>(
  component: (
    props: ValidatedWorkspacePageProps & T
  ) => Promise<React.ReactNode>
) {
  return async function WorkspacePageWrapper({
    params,
    ...additionalProps
  }: WorkspacePageProps & T) {
    const { slug } = await params;
    const validatedData = await validateWorkspacePage(slug);

    return component({
      ...validatedData,
      ...(additionalProps as unknown as T)
    });
  };
}
