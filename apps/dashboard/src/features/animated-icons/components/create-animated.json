{"v": "5.12.1", "fr": 60, "ip": 0, "op": 60, "w": 500, "h": 500, "nm": "system-regular-40-add-card", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-add-card", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 0, "k": [354.165, 145.831, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.6, "y": 0}, "t": 1, "s": [{"i": [[11.506, 0], [0, 0], [0, -11.505], [0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0], [11.506, 0], [0, 0], [0, -11.505]], "v": [[46.875, -67.709], [-46.875, -67.709], [-67.709, -46.875], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.709, -46.875]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[11.506, 0], [0, 0], [0, -11.505], [0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0], [11.506, 0], [0, 0], [0, -11.505]], "v": [[46.581, 26.291], [-47.169, 26.291], [-68.003, 47.125], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.415, 47.125]], "c": true}]}, {"t": 18, "s": [{"i": [[11.506, 0], [0, 0], [0, 0.034], [0, 0], [-11.506, 0], [0, 0], [0, -0.034], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, -0.034], [0, 0], [11.506, 0], [0, 0], [0, 0.034]], "v": [[46.581, 67.83], [-47.169, 67.83], [-68.003, 67.769], [-67.709, 67.77], [-46.875, 67.709], [46.875, 67.709], [67.709, 67.77], [67.415, 67.769]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 31.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 18, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.2], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1, "s": [0]}, {"t": 35, "s": [90]}], "ix": 10}, "p": {"a": 0, "k": [354.171, 354.168, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [354.171, 354.168, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[67.709, 0], [-67.709, 0]], "c": false}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[43.168, 0], [-43.168, 0]], "c": false}]}, {"t": 20, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[67.709, 0], [-67.709, 0]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 1, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -67.709], [0, 67.709]], "c": false}]}, {"i": {"x": 0.4, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -43.168], [0, 43.168]], "c": false}]}, {"t": 20, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -67.709], [0, 67.709]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 31.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [354.171, 354.168], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 60, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [145.831, 145.831, 0], "to": [34.722, 0, 0], "ti": [-34.722, 0, 0]}, {"t": 42, "s": [354.165, 145.831, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.506, 0], [0, 0], [0, -11.506], [0, 0], [-11.506, 0], [0, 0], [0, 11.505], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.505], [0, 0], [11.506, 0], [0, 0], [0, -11.506]], "v": [[46.875, -67.709], [-46.875, -67.709], [-67.709, -46.875], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.709, -46.875]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 31.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 60, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [145.831, 354.17, 0], "to": [0, -34.723, 0], "ti": [0, 34.723, 0]}, {"t": 49, "s": [145.831, 145.831, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.506, 0], [0, 0], [0, -11.505], [0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0], [11.506, 0], [0, 0], [0, -11.505]], "v": [[46.875, -67.709], [-46.875, -67.709], [-67.709, -46.875], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.709, -46.875]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 31.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 60, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [145.831, 354.17, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 23, "s": [{"i": [[11.506, 0], [0, 0], [0, 0.034], [0, 0], [-11.506, 0], [0, 0], [0, -0.034], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, -0.034], [0, 0], [11.506, 0], [0, 0], [0, 0.034]], "v": [[46.581, 67.83], [-47.169, 67.83], [-68.003, 67.769], [-67.709, 67.77], [-46.875, 67.709], [46.875, 67.709], [67.709, 67.77], [67.415, 67.769]], "c": true}]}, {"i": {"x": 0, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 25, "s": [{"i": [[11.506, 0], [0, 0], [0, -11.505], [0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0], [11.506, 0], [0, 0], [0, -11.505]], "v": [[46.581, 26.291], [-47.169, 26.291], [-68.003, 47.125], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.415, 47.125]], "c": true}]}, {"t": 57, "s": [{"i": [[11.506, 0], [0, 0], [0, -11.505], [0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0]], "o": [[0, 0], [-11.506, 0], [0, 0], [0, 11.506], [0, 0], [11.506, 0], [0, 0], [0, -11.505]], "v": [[46.875, -67.709], [-46.875, -67.709], [-67.709, -46.875], [-67.709, 46.875], [-46.875, 67.709], [46.875, 67.709], [67.709, 46.875], [67.709, -46.875]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 31.3, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 23, "op": 60, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.002, 250.002, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [2083, 2083, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.41, 0], [0, 0], [0, 0], [0.41, 0], [0, -0.41], [0, 0], [0, 0], [0, -0.41], [-0.41, 0], [0, 0], [0, 0], [-0.41, 0], [0, 0.41], [0, 0], [0, 0], [0, 0.41]], "o": [[0, 0], [0, 0], [0, -0.41], [-0.41, 0], [0, 0], [0, 0], [-0.41, 0], [0, 0.41], [0, 0], [0, 0], [0, 0.41], [0.41, 0], [0, 0], [0, 0], [0.41, 0], [0, -0.41]], "v": [[3.25, -0.75], [0.75, -0.75], [0.75, -3.25], [0, -4], [-0.75, -3.25], [-0.75, -0.75], [-3.25, -0.75], [-4, 0], [-3.25, 0.75], [-0.75, 0.75], [-0.75, 3.25], [0, 4], [0.75, 3.25], [0.75, 0.75], [3.25, 0.75], [4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [255, 255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.96, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [255, 245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [245, 245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [245, 255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 60, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": ".primary.design", "cl": "primary design", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250.002, 250.002, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [2083, 2083, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.41, 0], [0, 0], [0, 0], [0.41, 0], [0, -0.41], [0, 0], [0, 0], [0, -0.41], [-0.41, 0], [0, 0], [0, 0], [-0.41, 0], [0, 0.41], [0, 0], [0, 0], [0, 0.41]], "o": [[0, 0], [0, 0], [0, -0.41], [-0.41, 0], [0, 0], [0, 0], [-0.41, 0], [0, 0.41], [0, 0], [0, 0], [0, 0.41], [0.41, 0], [0, 0], [0, 0], [0.41, 0], [0, -0.41]], "v": [[3.25, -0.75], [0.75, -0.75], [0.75, -3.25], [0, -4], [-0.75, -3.25], [-0.75, -0.75], [-3.25, -0.75], [-4, 0], [-3.25, 0.75], [-0.75, 0.75], [-0.75, 3.25], [0, 4], [0.75, 3.25], [0.75, 0.75], [3.25, 0.75], [4, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [255, 255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.96, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [255, 245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [245, 245], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 3, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.14, 0], [0, 0], [0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14]], "o": [[0, 0.14], [0, 0], [-0.14, 0], [0, 0], [0, -0.14], [0, 0], [0.14, 0], [0, 0]], "v": [[2.5, 2.25], [2.25, 2.5], [-2.25, 2.5], [-2.5, 2.25], [-2.5, -2.25], [-2.25, -2.5], [2.25, -2.5], [2.5, -2.25]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.96, 0], [0, 0], [0, -0.96], [0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0]], "o": [[0, 0], [-0.97, 0], [0, 0], [0, 0.96], [0, 0], [0.96, 0], [0, 0], [0, -0.96]], "v": [[2.25, -4], [-2.25, -4], [-4, -2.25], [-4, 2.25], [-2.25, 4], [2.25, 4], [4, 2.25], [4, -2.25]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('system-regular-40-add-card').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [245, 255], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 1, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.071, 0.075, 0.192], "ix": 1}}]}], "ip": 0, "op": 131, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-add-card", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 500, "h": 500, "ip": 0, "op": 70, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-add-card", "dr": 60}], "props": {}}