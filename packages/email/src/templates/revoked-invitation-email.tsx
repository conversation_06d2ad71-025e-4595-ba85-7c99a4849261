/* eslint-disable @typescript-eslint/no-explicit-any */
import * as React from "react";
import { Body, Container, Head, Heading, Hr, Html, Preview, Text } from "@react-email/components";
import { Tailwind } from "@react-email/tailwind";

export type RevokedInvitationEmailProps = {
  appName: string;
  organizationName: string;
};

const HtmlCompat = Html as any;
const HeadCompat = Head as any;
const PreviewCompat = Preview as any;
const BodyCompat = Body as any;
const ContainerCompat = Container as any;
const HeadingCompat = Heading as any;
const TextCompat = Text as any;
const HrCompat = Hr as any;

export function RevokedInvitationEmail({
  appName,
  organizationName,
}: RevokedInvitationEmailProps): React.JSX.Element {
  return (
    <HtmlCompat>
      <HeadCompat />
      <PreviewCompat>
        Invitation for {organizationName} on {appName} revoked
      </PreviewCompat>
      <Tailwind>
        <BodyCompat className="m-auto bg-white px-2 font-sans">
          <ContainerCompat className="mx-auto my-[40px] max-w-[465px] rounded border border-solid border-[#eaeaea] p-[20px]">
            <HeadingCompat className="mx-0 my-[30px] p-0 text-center text-[24px] font-normal text-black">
              Invitation for <strong>{organizationName}</strong> on <strong>{appName}</strong>{" "}
              revoked
            </HeadingCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">Hello,</TextCompat>
            <TextCompat className="text-[14px] leading-[24px] text-black">
              Your invitation to join <strong>{organizationName}</strong> has been revoked.
            </TextCompat>
            <HrCompat className="mx-0 my-[26px] w-full border border-solid border-[#eaeaea]" />
            <TextCompat className="text-[12px] leading-[24px] text-[#666666]">
              If the revocation was unexpected, ask an admin on the organization to send you a new
              invitation link.
            </TextCompat>
          </ContainerCompat>
        </BodyCompat>
      </Tailwind>
    </HtmlCompat>
  );
}
