export const getFirstInitial = (name: string | null): string => {
  if (!name || name.trim() === "") return "?";
  const trimmedName = name.trim();
  const firstChar = trimmedName[0];
  return firstChar ? firstChar.toUpperCase() : "?";
};

export const getFirstLastInitials = (name: string | null): string => {
  if (!name || name.trim() === "") return "?";
  const trimmedName = name.trim();
  const names = trimmedName.split(" ").filter((name) => name.length > 0);

  if (names.length === 0) return "?";
  if (names.length === 1) {
    const firstChar = names[0]?.[0];
    return firstChar ? firstChar.toUpperCase() : "?";
  }

  const firstChar = names[0]?.[0];
  const lastChar = names[names.length - 1]?.[0];

  if (!firstChar || !lastChar) return "?";
  return (firstChar + lastChar).toUpperCase();
};
