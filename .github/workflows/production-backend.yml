name: Production Backend Deployment

on:
  push:
    branches: [main]
    paths:
      - "apps/backend/**"
      - "packages/**"
      - "!packages/ui/**"
      - "turbo.json"
      - "package.json"
      - "pnpm-lock.yaml"
      - "Dockerfile"
      - "fly.backend.toml"

concurrency:
  group: production-backend-${{ github.ref }}
  cancel-in-progress: true

jobs:
  quality-checks:
    name: Quality Checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Cache Turbo
        uses: actions/cache@v4
        with:
          path: .turbo
          key: turbo-${{ runner.os }}-${{ github.sha }}
          restore-keys: |
            turbo-${{ runner.os }}-

      - name: Generate Prisma Client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Run TypeScript type checking
        run: pnpm typecheck --filter=backend

      - name: Build backend
        run: pnpm build --filter=backend
        env:
          NODE_ENV: production
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
          BETTER_AUTH_URL: ${{ secrets.BETTER_AUTH_URL }}
          FRONTEND_URL: ${{ secrets.FRONTEND_URL }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
          EMAIL_FROM: ${{ secrets.EMAIL_FROM }}

  deploy:
    name: Deploy to Fly.io
    runs-on: ubuntu-latest
    needs: quality-checks
    environment:
      name: production
      url: https://centaly-backend.fly.dev
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Fly.io CLI
        uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Deploy to Fly.io
        run: flyctl deploy --config fly.backend.toml --remote-only
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Verify deployment
        run: |
          echo "🚀 Backend deployed to production"
          echo "Health check: https://centaly-backend.fly.dev/health"

          # Wait for deployment to be ready
          sleep 30

          # Health check
          curl -f https://centaly-backend.fly.dev/health || echo "Health check failed"

      - name: Comment deployment status
        uses: actions/github-script@v7
        if: github.event_name == 'push'
        with:
          script: |
            const deploymentUrl = 'https://centaly-backend.fly.dev';
            console.log(`🚀 Production backend deployed to: ${deploymentUrl}`);
            console.log(`📊 Health check: ${deploymentUrl}/health`);

  database-push:
    name: Push Database Schema
    runs-on: ubuntu-latest
    needs: deploy
    if: contains(github.event.head_commit.message, '[schema]') || contains(github.event.head_commit.message, '[db]')
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma Client
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm db:generate

      - name: Push Database Schema
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm db:push
