import {
  DeleteObjectCommand,
  PutObjectCommand,
  S3Client
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Configuration validation
const requiredEnvVars = [
  "AWS_S3_BUCKET_NAME",
  "AWS_REGION",
  "AWS_ACCESS_KEY_ID",
  "AWS_SECRET_ACCESS_KEY"
];

// Validate environment variables
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

const BUCKET_NAME = process.env.AWS_S3_BUCKET_NAME!;

// Allowed file types and their corresponding MIME types
const ALLOWED_FILE_TYPES = {
  "image/png": ".png",
  "image/jpeg": ".jpg",
  "image/jpg": ".jpg",
  "image/gif": ".gif"
} as const;

type AllowedMimeType = keyof typeof ALLOWED_FILE_TYPES;

// Maximum file size (10MB in bytes)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

export interface PresignedUploadResult {
  uploadUrl: string;
  fileUrl: string;
  key: string;
}

/**
 * Generate a presigned URL for uploading workspace logos to S3
 */
export async function generatePresignedUploadUrl(
  organizationId: string,
  fileName: string,
  fileType: string,
  fileSize: number
): Promise<PresignedUploadResult> {
  // Validate file type
  if (!Object.keys(ALLOWED_FILE_TYPES).includes(fileType)) {
    throw new Error(
      `File type ${fileType} is not allowed. Allowed types: PNG, JPEG, GIF`
    );
  }

  // Validate file size (10MB)
  if (fileSize > MAX_FILE_SIZE) {
    throw new Error(
      `File size ${fileSize} bytes exceeds maximum allowed size of ${MAX_FILE_SIZE} bytes (10MB)`
    );
  }

  // Generate unique key for the file
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  const extension = ALLOWED_FILE_TYPES[fileType as AllowedMimeType];
  const key = `workspace-logos/${organizationId}/${timestamp}-${randomString}${extension}`;

  // Create put object command
  const putObjectCommand = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: fileType,
    // Remove ContentLength as it can cause issues with presigned URLs
    // The browser will set this automatically
    // ContentLength: fileSize,
    // Remove ServerSideEncryption for now to avoid potential conflicts
    // ServerSideEncryption: "AES256",
    CacheControl: "max-age=31536000" // 1 year
  });

  try {
    // Generate presigned URL (expires in 5 minutes)
    const uploadUrl = await getSignedUrl(s3Client, putObjectCommand, {
      expiresIn: 300
    });

    // Construct the public URL for the uploaded file
    const fileUrl = `https://${BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`;

    console.log("Generated presigned URL for key:", key);
    console.log(
      "Upload URL (first 100 chars):",
      uploadUrl.substring(0, 100) + "..."
    );

    return {
      uploadUrl,
      fileUrl,
      key
    };
  } catch (error) {
    console.error("Error generating presigned URL:", error);
    console.error("Failed to generate presigned URL - check AWS configuration");
    throw new Error("Failed to generate upload URL");
  }
}

/**
 * Delete a file from S3
 */
export async function deleteFile(fileUrl: string): Promise<void> {
  try {
    // Extract key from file URL
    const url = new URL(fileUrl);
    const key = url.pathname.substring(1); // Remove leading slash

    // Validate that this is indeed a file from our bucket
    if (!key.startsWith("workspace-logos/")) {
      throw new Error("Invalid file key - not a workspace logo");
    }

    const deleteCommand = new DeleteObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key
    });

    await s3Client.send(deleteCommand);
  } catch (error) {
    console.error("Error deleting file from S3:", error);
    throw new Error("Failed to delete file");
  }
}

/**
 * Delete old workspace logo when uploading a new one
 */
export async function deleteOldWorkspaceLogo(
  oldLogoUrl: string | null
): Promise<void> {
  if (!oldLogoUrl) return;

  try {
    await deleteFile(oldLogoUrl);
  } catch (error) {
    // Log error but don't fail the upload if we can't delete the old file
    console.warn("Failed to delete old workspace logo:", error);
  }
}

/**
 * Validate if a file type is allowed
 */
export function isAllowedFileType(mimeType: string): boolean {
  return Object.keys(ALLOWED_FILE_TYPES).includes(mimeType);
}

/**
 * Validate if file size is within limits
 */
export function isValidFileSize(size: number): boolean {
  return size > 0 && size <= MAX_FILE_SIZE;
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Validate that a URL belongs to the application's S3 bucket
 */
export function isValidS3LogoUrl(logoUrl: string): boolean {
  try {
    const url = new URL(logoUrl);

    // Check if the hostname matches our S3 bucket pattern
    const expectedHostname = `${BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com`;
    if (url.hostname !== expectedHostname) {
      return false;
    }

    // Check if the path starts with workspace-logos/
    const key = url.pathname.substring(1); // Remove leading slash
    if (!key.startsWith("workspace-logos/")) {
      return false;
    }

    // Additional validation: ensure the path structure is correct
    // Expected format: workspace-logos/{organizationId}/{timestamp}-{randomString}.{extension}
    const pathParts = key.split("/");
    if (pathParts.length !== 3 || pathParts[0] !== "workspace-logos") {
      return false;
    }

    // Validate file extension
    const filename = pathParts[2];
    if (!filename) {
      return false;
    }
    const hasValidExtension = Object.values(ALLOWED_FILE_TYPES).some((ext) =>
      filename.endsWith(ext)
    );
    if (!hasValidExtension) {
      return false;
    }

    return true;
  } catch {
    // Invalid URL format
    return false;
  }
}
