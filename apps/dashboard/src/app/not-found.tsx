import Link from "next/link";
import { verifySession } from "@/src/features/auth/dal";

export const dynamic = "force-dynamic";

export default async function NotFound() {
  const session = await verifySession();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-4xl font-bold">404</h1>
      <p className="text-xl mt-4">Page not found</p>
      <div className="mt-6">
        {session?.user ? (
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Return to Dashboard
          </Link>
        ) : (
          <Link
            href="/sign-in"
            className="text-blue-600 hover:text-blue-800 underline"
          >
            Sign In
          </Link>
        )}
      </div>
    </div>
  );
}
