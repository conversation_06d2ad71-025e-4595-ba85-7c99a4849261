# Resend Email Invitation Feature - Implementation Plan

## Overview

The **Resend Email Invitation** feature enhances the existing Team Member Management system by enabling users to resend invitation emails to pending invitees. This feature integrates seamlessly with the current Better-Auth organization plugin and provides immediate user feedback through toast notifications.

## Feature Requirements

### Primary Functionality

- **Trigger**: User clicks "Resend invite" dropdown menu item for pending invitees
- **Action**: Call `authClient.organization.inviteMember()` with `resend: true` parameter
- **Feedback**: Display success, loading, and error states via toast notifications
- **Integration**: Seamlessly integrate with existing table state management

### User Experience Requirements

- Immediate loading state indication when resend is initiated
- Success confirmation when email is sent successfully
- Clear error messaging if resend fails
- No disruption to table filtering or pagination state

## Technical Implementation Plan

### 1. Component Modifications

#### A. Update `team-members-table.tsx` (Line 330)

**Current State:**

```typescript
const handleResendInvite = async () => {
  // For pending invitations, we'll need to get a fresh invitation link
  // Since Better-Au<PERSON> doesn't have a direct "resend" method, we can copy the invite link
  // or implement a manual email resend if needed
  console.log("resendInvite", data);
  // TODO: Implement invite link copying or email resending
};
```

**Required Changes:**

- Replace placeholder implementation with actual Better-Auth integration
- Add toast notifications for loading, success, and error states
- Integrate with React Query for cache invalidation
- Handle async operation with proper error handling

#### B. Hook Integration Requirements

**Import Dependencies:**

```typescript
import { authClient } from "@repo/authentication/auth-client";
import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
```

**State Management:**

- Utilize existing `useMemberManagement` hook pattern for consistency
- Add mutation for resend invitation functionality
- Implement optimistic updates for immediate UI feedback

### 2. Implementation Details

#### A. Better-Auth Integration

**Function Call Structure:**

```typescript
await authClient.organization.inviteMember({
  email: data.user.email,
  role: data.role,
  organizationId: activeOrganization.id,
  resend: true,
});
```

**Key Parameters:**

- `email`: Extract from `data.user.email` (pending invitee's email)
- `role`: Maintain current role from `data.role`
- `organizationId`: Use active organization context
- `resend: true`: Critical parameter to trigger email resend

#### B. Toast Notification Implementation

**Loading State:**

```typescript
const resendPromise = authClient.organization.inviteMember({...});
toast.promise(resendPromise, {
  loading: "Resending invitation...",
  success: "Invitation resent successfully",
  error: (err) => err.message || "Failed to resend invitation"
});
```

**Alternative Manual Approach:**

```typescript
try {
  toast.loading("Resending invitation...");
  await authClient.organization.inviteMember({...});
  toast.success("Invitation resent successfully");
} catch (error) {
  toast.error(error.message || "Failed to resend invitation");
}
```

#### C. React Query Integration

**Cache Management:**

```typescript
const queryClient = useQueryClient();

// After successful resend
queryClient.invalidateQueries({
  queryKey: ["members", organizationSlug],
});
```

**Mutation Hook Pattern:**

```typescript
const resendInviteMutation = useMutation({
  mutationFn: async ({ email, role, organizationId }: ResendInviteParams) => {
    return authClient.organization.inviteMember({
      email,
      role,
      organizationId,
      resend: true,
    });
  },
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ["members", organizationSlug] });
    toast.success("Invitation resent successfully");
  },
  onError: (error) => {
    toast.error(error.message || "Failed to resend invitation");
  },
});
```

### 3. Code Organization

#### A. Hook Extension

**Location:** `apps/dashboard/src/features/settings-members/hooks/use-members.ts`

**Add Function:**

```typescript
export function useResendInvitation(organizationSlug: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ email, role }: { email: string; role: string }) => {
      // Implementation details
    },
    // Success/error handlers
  });
}
```

#### B. Component Integration

**Location:** `apps/dashboard/src/features/settings-members/team-members-table.tsx`

**Integration Points:**

- Import new hook: `useResendInvitation`
- Replace existing `handleResendInvite` function
- Add loading state management
- Maintain dropdown menu state consistency

### 4. Error Handling Strategy

#### A. Better-Auth Error Handling

- Catch and parse Better-Auth specific errors
- Handle network connectivity issues
- Provide fallback error messages for unknown errors

#### B. User Experience Considerations

- Disable resend button during operation
- Maintain button state consistency
- Handle edge cases (missing organization, invalid email)

#### C. Error Message Standards

```typescript
const getErrorMessage = (error: unknown) => {
  if (error instanceof Error) return error.message;
  if (typeof error === "string") return error;
  return "Failed to resend invitation. Please try again.";
};
```

### 6. Performance Considerations

#### A. Debouncing

- Implement button debouncing to prevent double-clicks
- Add minimum delay between resend attempts

#### B. Cache Management

- Efficient cache invalidation strategy
- Optimize React Query refetch behavior

#### C. Network Optimization

- Handle slow network connections gracefully
- Implement request timeout handling

## File Modification Summary

### Primary Files to Modify

1. **`apps/dashboard/src/features/settings-members/team-members-table.tsx`**
   - Update `handleResendInvite` function (line 330-336)
   - Add necessary imports
   - Integrate with mutation hook

2. **`apps/dashboard/src/features/settings-members/hooks/use-members.ts`**
   - Add `useResendInvitation` hook
   - Follow existing pattern from `useMemberManagement`

### Supporting Files (Reference Only)

1. **`packages/ui/src/components/sonner.tsx`** - Toast component (no changes needed)
2. **`apps/dashboard/src/features/settings-members/team-member-invite-modal.tsx`** - Reference implementation

## Implementation Timeline

### Phase 1: Core Implementation

1. Implement `useResendInvitation` hook
2. Update `handleResendInvite` function
3. Add toast notifications
4. Basic error handling

### Phase 2: Polish

1. Enhanced error handling
2. Loading state refinements
3. User experience improvements
4. Code review and cleanup

## Acceptance Criteria

### Functional Requirements

- [x] User can click "Resend invite" for pending invitees
- [x] System calls Better-Auth with correct parameters
- [x] Success toast appears when email is sent
- [x] Error toast appears when resend fails
- [x] Loading state is shown during operation
- [x] Table state remains consistent after operation

### Technical Requirements

- [x] Integration with existing React Query patterns
- [x] Proper error handling and user feedback
- [x] Cache invalidation after successful resend
- [x] Consistent code style with existing codebase
- [x] No breaking changes to existing functionality

### User Experience Requirements

- [x] Immediate feedback on user action
- [x] Clear success/error messaging
- [x] No unexpected UI behavior
- [x] Accessible interaction patterns
- [x] Consistent with existing feature behavior

## References

- [Better-Auth Organization Plugin Documentation](https://www.better-auth.com/docs/plugins/organization#send-invitation)
- [Sonner Toast Documentation](https://sonner.emilkowal.ski/toast)
- [Team Member Management Feature Overview](./team-member-management.md)
- Existing implementation: `team-member-invite-modal.tsx:177-188`
- Toast usage pattern: `team-member-invite-modal.tsx:194-198`
