---
description:
globs:
alwaysApply: false
---

# Workspace Slug Validation and Persistence

This document explains the implementation of server-side slug validation with slug persistence across all routes under the `[slug]` directory.

## Overview

We've implemented a comprehensive solution that:

1. **Validates workspace access server-side** for all pages under `/[slug]/` routes
2. **Prevents invalid slugs from loading pages** by redirecting unauthorized users
3. **Provides slug persistence** through server-side props and client-side context
4. **Offers reusable utilities** for consistent validation patterns

## Solution Components

### 1. Page Validation Utilities (`contexts/page-validation.ts`)

```typescript
import {
  validateWorkspaceAccess,
  validateWorkspacePage,
  type WorkspacePageProps,
} from "@/src/features/auth/contexts/page-validation";

// Minimal validation (when you don't need workspace data)
await validateWorkspaceAccess(slug);

// Full validation with data
const { session, organizations, currentWorkspace } =
  await validateWorkspacePage(slug);

// Type-safe page props interface
interface PageProps {
  params: Promise<{ slug: string }>;
  // your additional props
}
```

**Key Features:**

- `validateWorkspaceAccess(slug)`: Minimal validation without returning data
- `validateWorkspacePage(slug)`: Server-side validation function that calls `requireWorkspaceAccess`
- `ValidatedWorkspacePageProps`: Type-safe interface for validated workspace data
- `createWorkspacePage()`: Higher-order function for creating validated page components

### 2. Validated Workspace Context (`contexts/validated-workspace-context.tsx`)

```typescript
import { ValidatedWorkspaceProvider, useValidatedWorkspace } from "@/src/features/auth/contexts/validated-workspace-context";

// In your page component
<ValidatedWorkspaceProvider
  slug={slug}
  session={session}
  organizations={organizations}
  currentWorkspace={currentWorkspace}
>
  {children}
</ValidatedWorkspaceProvider>

// In client components
const { slug, currentWorkspace, organizations, session } = useValidatedWorkspace();
```

**Key Features:**

- Provides server-validated data to client components without loading states
- Includes helper methods for workspace access checks
- Type-safe workspace data access

### 3. Current Implementation Pattern

Real pages use direct validation without context wrapping:

- Direct calls to `validateWorkspaceAccess(slug)` for validation only
- Direct calls to `validateWorkspacePage(slug)` when workspace data is needed
- No ValidatedWorkspaceProvider wrapping in current implementation

## Implementation Pattern

### For Each Page Under `/[slug]/`

1. **Import validation utilities:**

```typescript
import {
  validateWorkspaceAccess,
  validateWorkspacePage,
} from "../../../../features/auth/contexts/page-validation";
```

2. **Use type-safe page props:**

```typescript
interface PageProps {
  params: Promise<{ slug: string }>;
}

export default async function YourPage({ params }: PageProps) {
  const { slug } = await params;
  // ...
}
```

3. **Validate workspace access:**

```typescript
// Option A: When you need workspace data
const { session, organizations, currentWorkspace } =
  await validateWorkspacePage(slug);

// Option B: When you only need validation (no unused variable warnings)
await validateWorkspaceAccess(slug);

// Option C: When you need only specific data
const { session } = await validateWorkspacePage(slug);
```

4. **Return page content:**

```typescript
// Current implementation pattern - direct validation without context
return (
  <div>
    {/* Your page content - no provider wrapping needed */}
  </div>
);
```

## Security Benefits

### Server-Side Validation

- Every page under `/[slug]/` validates workspace access before rendering
- Invalid slugs trigger redirects via `requireWorkspaceAccess()`
- Prevents unauthorized access to workspace data

### Consistent Validation

- All pages use the same validation pattern
- Centralized validation logic in `validateWorkspacePage()`
- Type-safe implementation reduces errors

## Slug Persistence

### Current Implementation

- Server-side validation occurs on each page load
- Slug is available through the params in page components
- No client-side context is currently implemented in the actual pages

### ValidatedWorkspaceProvider (Available but Unused)

The context provider exists but is not currently used in the actual page implementations:

```typescript
// Available for future use
const { slug, currentWorkspace } = useValidatedWorkspace();
```

### Navigation Links

```typescript
// ✅ Manual slug inclusion in navigation
<Link href={`/${slug}/docs`}>Documentation</Link>
<Link href={`/${slug}/settings/general`}>Settings</Link>
```
