"use client";

import { authClient } from "@repo/authentication/auth-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

interface UpdateWorkspaceNameData {
  organizationId: string;
  name: string;
}

export function useUpdateWorkspaceName() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ organizationId, name }: UpdateWorkspaceNameData) => {
      const { data, error } = await authClient.organization.update({
        organizationId,
        data: { name }
      });

      if (error) {
        throw new Error(error.message || "Failed to update workspace name");
      }

      return data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch organization queries
      queryClient.invalidateQueries({ queryKey: ["organizations"] });
      queryClient.invalidateQueries({ queryKey: ["activeOrganization"] });

      toast.success("Workspace name updated successfully");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update workspace name");
    }
  });
}
