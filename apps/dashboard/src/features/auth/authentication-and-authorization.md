# Authentication and Authorization

The authentication and authorization system is a comprehensive solution built on Better-Auth that handles user registration, login, email verification, password reset, and organizational onboarding flows.

## Route Structure

**(auth) route group**: Contains all authentication-related pages

- `/sign-in` - User login page
- `/sign-up` - User registration page
- `/verify` - Email verification code entry
- `/verify-email/[token]` - Automatic email verification via token
- `/reset-password` - Password reset request
- `/reset-password/new` - Set new password after reset

**onboarding directory**: Contains post-authentication setup flow

- `/onboarding/workspace` - Create new workspace/organization
- `/onboarding/invite` - Invite team members to workspace
- `/onboarding/accept-invitation/[id]` - Accept or decline workspace invitations

## Session Management

Better-Auth manages sessions using traditional cookie-based authentication:

- **Client-side**: `useSession` hook from `@repo/authentication/auth-client` provides reactive session access
- **Cookie**: `better-auth.session_token` contains the session identifier

Example usage:

```typescript
const { data: session, isPending } = useSession();
const sessionData = await authClient.getSession();
```

## Auth Redirects in Page.tsx Server Components

The authentication system uses optimized server-side redirects in `page.tsx` components to provide seamless user experience while minimizing API calls. This approach leverages Next.js App Router's server components for efficient route protection.

### Redirect Strategy

Auth pages use `getAuthenticatedUserRedirect()` to perform all authentication checks in a single API call:

```typescript
export default async function SignInPage() {
  // Single API call that handles all auth checks (session, email verification, onboarding, organizations)
  // Returns "/sign-in" only if user is not authenticated (no session or API error)
  // Returns other destinations if user has valid session but needs redirecting
  const redirectTo = await getAuthenticatedUserRedirect();

  if (redirectTo !== "/sign-in") {
    // User has valid session and should be redirected to appropriate destination
    // (workspace, onboarding, verification, etc.)
    redirect(redirectTo);
  }

  // User is not authenticated or API error occurred - show sign-in form
  return <SignInForm />;
}
```

### Performance Optimization

This pattern minimises API calls by:

- **Direct workspace redirects**: Auth pages redirect directly to workspaces instead of going through home page
- **Centralized logic**: All redirect decisions happen in one function
- **Eliminated intermediate redirects**: No more auth → home → workspace redirect chain

### Redirect Flow Examples

1. **Unauthenticated user visits `/sign-up`**: Shows sign-up form
2. **Authenticated user visits `/sign-up`**: Redirects directly to `/{workspace}/home`
3. **User needs email verification**: Redirects to `/verify`
4. **User needs onboarding**: Redirects to `/onboarding/workspace`

## Data Access Layer (DAL) for Authentication

The authentication DAL (`authentication.ts`) provides a comprehensive server-side API for managing authentication state, user sessions, and workspace access. It's designed for performance with React's `cache()` function to minimize duplicate API calls within request cycles.

### Core Authentication Functions

#### Session Management

```typescript
// Cached session verification - foundation for all auth checks
export const verifySession = cache(async (): Promise<Session | null> => {
  // Makes server-to-server call to Hono backend Better-Auth endpoint
  const response = await fetch(`${API_URL}/api/auth/get-session`, {
    headers: { Cookie: headersList.get("cookie") || "" },
    cache: "no-store",
    credentials: "include"
  });
  return response.ok ? await response.json() : null;
});

// Progressive authentication requirements
export async function requireAuth(): Promise<Session>; // Basic auth
export async function requireVerifiedUser(): Promise<Session>; // + email verified
export async function requireOnboardedUser(): Promise<Session>; // + onboarding complete
```

#### Workspace Management

```typescript
// Cached organization fetching
export const getUserOrganizations = cache(async (): Promise<Organization[]> => {
  // Fetches user's organizations from Hono backend
});

// Workspace access verification
export async function requireWorkspaceAccess(slug: string): Promise<{
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization | undefined;
}>;

// Optimized workspace data for redirected pages
export async function getWorkspaceData(slug: string): Promise<{
  session: Session;
  organizations: Organization[];
  currentWorkspace: Organization;
}>;
```

#### Smart Redirect Logic

```typescript
// Centralized redirect destination logic
export async function getAuthenticatedUserRedirect(): Promise<string> {
  // Performs comprehensive auth checks and returns appropriate destination:
  // - "/sign-in" for unauthenticated users
  // - "/verify" for unverified emails
  // - "/onboarding/*" for incomplete onboarding
  // - "/{workspace}/home" for complete users
}
```

### Performance Features

- **React Cache Integration**: Uses `cache()` to prevent duplicate API calls within request cycles
- **Optimized Workspace Access**: `getWorkspaceData()` assumes valid access for better performance
- **Single-Call Redirects**: `getAuthenticatedUserRedirect()` consolidates multiple checks

## Integration with Better-Auth and Hono.js

The authentication system creates a seamless bridge between the Next.js frontend, Hono.js backend, and Better-Auth authentication provider.

### Architecture Flow

```
Next.js Frontend (Dashboard)
    ↓ Server-to-Server Calls
Hono.js Backend API
    ↓ Better-Auth Integration
Better-Auth Session Management
    ↓ Database Storage
PlanetScale MySQL Database
```

### Better-Auth Integration

**Backend (Hono.js)**:

```typescript
// Better-Auth configuration in Hono backend
import { auth } from "@repo/authentication/auth";

// Session endpoint that Next.js calls
app.get("/api/auth/get-session", async (c) => {
  const session = await auth.api.getSession({
    headers: c.req.headers
  });
  return c.json(session);
});
```

**Frontend (Next.js)**:

```typescript
// DAL makes server-to-server calls to Hono backend
const response = await fetch(`${API_URL}/api/auth/get-session`, {
  headers: {
    Cookie: headersList.get("cookie") || "",
    "X-Forwarded-For": headersList.get("x-forwarded-for") || "",
    "User-Agent": headersList.get("user-agent") || ""
  },
  credentials: "include"
});
```

### Session Cookie Flow

1. **User authenticates** via Better-Auth in Hono backend
2. **Better-Auth sets session cookie** (`better-auth.session_token`)
3. **Next.js forwards cookie** in server-to-server calls
4. **Hono backend validates** session with Better-Auth
5. **Session data returned** to Next.js for routing decisions

## Key Integration Points

### 1. Server-to-Server Communication

**Authentication Flow**:

- Next.js server components call Hono.js endpoints
- Cookies are forwarded from browser → Next.js → Hono.js → Better-Auth
- Response flows back through the same chain

**API Endpoints Used**:

- `GET /api/auth/get-session` - Session verification
- `GET /api/v1/profile/organizations` - User's organizations
- `POST /api/auth/*` - Authentication actions (sign-in, sign-up, etc.)

### 2. Route Protection Strategy

**Three-Layer Protection**:

1. **Page-level**: `requireAuth()`, `requireVerifiedUser()`, `requireOnboardedUser()`
2. **Workspace-level**: `requireWorkspaceAccess()`, `requireWorkspaceRole()`
3. **Permission-level**: `hasWorkspacePermission()`

### 3. Error Handling & Fallbacks

**Graceful Degradation**:

```typescript
export const verifySession = cache(async (): Promise<Session | null> => {
  try {
    const response = await fetch(/* ... */);
    return response.ok ? await response.json() : null;
  } catch (error) {
    console.error("Session verification error:", error);
    return null; // Graceful fallback
  }
});
```

**Redirect Safety**:

- API errors result in safe fallbacks (usually sign-in page)
- Network issues don't crash the application
- Invalid sessions are handled gracefully
