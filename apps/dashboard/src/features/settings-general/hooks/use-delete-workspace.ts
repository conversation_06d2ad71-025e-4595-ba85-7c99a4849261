"use client";

import { use<PERSON>outer } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import type { DeleteWorkspaceFormData } from "../schemas/delete-workspace-schema";
import { handlePostDeletionRedirect } from "../utils/post-deletion-redirect";

/**
 * Response interface from the delete workspace API endpoint
 */
interface DeleteWorkspaceResponse {
  /** Whether the deletion was successful */
  success: boolean;
  /** Array of remaining workspaces the user has access to after deletion */
  remainingWorkspaces: Array<{
    /** Organization slug for routing */
    slug: string;
    /** Human-readable organization name */
    name: string;
  }>;
  /** Whether the deleted workspace was the user's default workspace */
  wasDefaultWorkspace: boolean;
}

/**
 * Client-side function to make the API call for deleting a workspace
 *
 * @param organizationSlug - The slug of the organization (workspace) to delete
 * @param data - Form data containing confirmation slug, reason, and optional feedback
 * @returns Promise that resolves to the deletion response data
 * @throws Error if the API call fails or returns an error response
 *
 * @example
 * ```typescript
 * const response = await deleteWorkspaceClient('my-org', {
 *   confirmationSlug: 'my-org',
 *   reason: 'no-longer-needed',
 *   feedback: 'Found a better solution'
 * });
 * ```
 */
async function deleteWorkspaceClient(
  organizationSlug: string,
  data: DeleteWorkspaceFormData
): Promise<DeleteWorkspaceResponse> {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001"}/api/v1/organizations/${organizationSlug}`,
    {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(data),
      credentials: "include"
    }
  );

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(
      errorData.error ||
        `Failed to delete workspace: ${response.status} ${response.statusText}`
    );
  }

  return response.json();
}

/**
 * React Query mutation hook for deleting a workspace (organization)
 *
 * This hook provides a complete workflow for workspace deletion including:
 * - API call to delete the workspace
 * - Cache invalidation to ensure UI consistency
 * - User feedback via toast notifications
 * - Intelligent redirection based on remaining workspaces
 * - Error handling with user-friendly messages
 *
 * **Security Requirements:**
 * - User must be authenticated
 * - User must have "owner" role in the organization
 * - Confirmation slug must match the organization slug
 * - A deletion reason must be provided
 *
 * **Post-Deletion Behavior:**
 * - If no remaining workspaces: redirects to /sign-up (with session cleanup)
 * - If remaining workspaces exist: redirects to next available workspace
 * - If deleted workspace was default: updates user's default workspace
 *
 * @returns UseMutationResult with mutate function that accepts organizationSlug and form data
 *
 * @example
 * ```typescript
 * function DeleteWorkspaceComponent() {
 *   const deleteWorkspace = useDeleteWorkspace();
 *
 *   const handleDelete = () => {
 *     deleteWorkspace.mutate({
 *       organizationSlug: 'my-workspace',
 *       data: {
 *         confirmationSlug: 'my-workspace',
 *         reason: 'no-longer-needed',
 *         feedback: 'Optional feedback'
 *       }
 *     });
 *   };
 *
 *   return (
 *     <button
 *       onClick={handleDelete}
 *       disabled={deleteWorkspace.isPending}
 *     >
 *       {deleteWorkspace.isPending ? 'Deleting...' : 'Delete Workspace'}
 *     </button>
 *   );
 * }
 * ```
 *
 * @see {@link DeleteWorkspaceFormData} for form data structure
 * @see {@link handlePostDeletionRedirect} for redirection logic
 */
export function useDeleteWorkspace() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation({
    mutationFn: ({
      organizationSlug,
      data
    }: {
      organizationSlug: string;
      data: DeleteWorkspaceFormData;
    }) => deleteWorkspaceClient(organizationSlug, data),

    onSuccess: async (response) => {
      // Clear all cached data to ensure UI consistency after deletion
      queryClient.clear();

      toast.success("Workspace deleted successfully");

      // Handle intelligent redirection based on remaining workspaces
      await handlePostDeletionRedirect(response, router);
    },

    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete workspace");
    }
  });
}
