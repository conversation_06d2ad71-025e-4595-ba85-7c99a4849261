import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>r,
  Html,
  Img,
  Link,
  Preview,
  Row,
  Section,
  Tailwind,
  Text
} from "@react-email/components";

export interface ResetPasswordEmailProps {
  name?: string;
  resetLink?: string;
}

export const ResetPasswordEmail = ({ resetLink }: ResetPasswordEmailProps) => {
  const previewText = `Reset your Centaly password`;
  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Tailwind>
        <Body className="bg-[#FCFCFD] my-auto mx-auto font-sans py-[32px] px-6">
          <Container className="bg-white border border-solid border-[#E1E1E6] rounded-xl mx-auto py-9 px-10 max-w-[674px] mt-[32px] mb-[24px]">
            <Heading className="text-[#2C333B] text-[24px] font-normal text-left p-0 mb-[30px] mx-0">
              Reset your password
            </Heading>
            <Text className="text-[#2C333B] text-[15px] leading-[24px]">
              We received a request to reset your password for your Centaly
              account. If you didn&apos;t make this request, you can safely
              ignore this email.
            </Text>
            <Section className="text-center mt-[32px] mb-[32px]">
              <Button
                className="bg-[#5753c6] rounded-md text-white text-[14px] no-underline text-center px-5 py-3"
                href={resetLink}
              >
                Reset Password
              </Button>
            </Section>
            <Text className="text-[#2C333B] text-[15px] leading-[24px]">
              Or copy and paste this URL into your browser:{" "}
              <Link
                href={resetLink}
                className="text-[#5753c6] text-[16px] leading-[24px]"
              >
                {resetLink}
              </Link>
            </Text>
            <Hr className="border border-solid border-[#E1E1E6] my-[32px] mx-0 w-full" />
            <Text className="text-[#666666] text-[12px] leading-[24px]">
              If you didn&apos;t request a password reset, please ignore this
              email or contact support if you have concerns.
            </Text>
          </Container>
          <Section className="text-center">
            <table className="w-full">
              <tr className="w-full">
                <td align="center">
                  <Link href="https://centaly.com">
                    <Img
                      alt="Centaly logo"
                      height="32"
                      src="https://app.centaly.com/brand/centaly-logo.svg"
                    />
                  </Link>
                </td>
              </tr>
              <tr>
                <td align="center">
                  <Row className="table-cell h-[42px] w-[56px] align-bottom">
                    <Column className="pr-[8px]">
                      <Link href="#">
                        <Img
                          alt="X"
                          height="24"
                          src="https://react.email/static/x-logo.png"
                          width="24"
                        />
                      </Link>
                    </Column>
                    <Column>
                      <Link href="#">
                        <Img
                          alt="Instagram"
                          height="24"
                          src="https://react.email/static/instagram-logo.png"
                          width="24"
                        />
                      </Link>
                    </Column>
                  </Row>
                </td>
              </tr>
              <tr>
                <td align="center">
                  <Text className="my-[8px] text-[12px] leading-[18px] text-[#63636c]">
                    Centaly HQ, Main Gate Rd, Kent ME4 4TZ
                  </Text>
                </td>
              </tr>
            </table>
          </Section>
        </Body>
      </Tailwind>
    </Html>
  );
};

export function reactResetPasswordEmail(props: ResetPasswordEmailProps) {
  return <ResetPasswordEmail {...props} />;
}
