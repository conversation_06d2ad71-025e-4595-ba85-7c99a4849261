import { createRoute, OpenAPIHono, z } from "@hono/zod-openapi";
import { prisma } from "@repo/database/client";

import { schemas, type Variables } from "../lib/openapi.js";
import { notFound, unauthorized } from "../middleware/error-handler.js";

// Todo response schema without internal IDs
const todoResponseSchema = z.object({
  id: z.number().int(),
  text: z.string(),
  completed: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime()
});

// Create todos router
export const todosRouter = new OpenAPIHono<{ Variables: Variables }>();

// List todos for organization
const listTodosRoute = createRoute({
  method: "get",
  path: "/organizations/{organizationId}/todos",
  tags: ["Todos"],
  summary: "List todos for organization",
  description: "Get a paginated list of todos for a specific organization",
  request: {
    params: z.object({
      organizationId: z.string()
    }),
    query: z.object({
      page: z.coerce.number().int().positive().default(1).optional(),
      pageSize: z.coerce
        .number()
        .int()
        .positive()
        .max(100)
        .default(20)
        .optional(),
      completed: z.coerce.boolean().optional()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            todos: z.array(todoResponseSchema),
            pagination: schemas.pagination
          })
        }
      },
      description: "List of todos"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Forbidden - user doesn't have access to this organization"
    }
  }
});

todosRouter.openapi(listTodosRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId } = c.req.param();
  const query = c.req.query();
  const page = Number(query.page) || 1;
  const pageSize = Number(query.pageSize) || 20;
  const completed = query.completed !== undefined ? query.completed === "true" : undefined;

  // Check if user has access to this organization
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId
    }
  });

  if (!membership) {
    return c.json(
      {
        error: "Forbidden - you don't have access to this organization",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  // Build where clause
  const whereClause = {
    organizationId,
    ...(completed !== undefined && { completed })
  };

  // Get total count
  const total = await prisma.todo.count({
    where: whereClause
  });

  // Get todos with pagination
  const todos = await prisma.todo.findMany({
    where: whereClause,
    orderBy: {
      createdAt: "desc"
    },
    skip: (page - 1) * pageSize,
    take: pageSize
  });

  const formattedTodos = todos.map((todo) => ({
    id: todo.id,
    text: todo.text,
    completed: todo.completed,
    createdAt: todo.createdAt.toISOString(),
    updatedAt: todo.updatedAt.toISOString()
  }));

  return c.json(
    {
      todos: formattedTodos,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    },
    200
  );
});

// Get todo by ID
const getTodoRoute = createRoute({
  method: "get",
  path: "/organizations/{organizationId}/todos/{id}",
  tags: ["Todos"],
  summary: "Get todo by ID",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int()
    })
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: todoResponseSchema
        }
      },
      description: "Todo found"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Forbidden"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Todo not found"
    }
  }
});

todosRouter.openapi(getTodoRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();

  // Check if user has access to this organization
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId
    }
  });

  if (!membership) {
    return c.json(
      {
        error: "Forbidden - you don't have access to this organization",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  const todo = await prisma.todo.findFirst({
    where: {
      id: Number(id),
      organizationId
    }
  });

  if (!todo) {
    return notFound("Todo not found");
  }

  return c.json(
    {
      id: todo.id,
      text: todo.text,
      completed: todo.completed,
      createdAt: todo.createdAt.toISOString(),
      updatedAt: todo.updatedAt.toISOString()
    },
    200
  );
});

// Create todo
const createTodoRoute = createRoute({
  method: "post",
  path: "/organizations/{organizationId}/todos",
  tags: ["Todos"],
  summary: "Create a new todo",
  request: {
    params: z.object({
      organizationId: z.string()
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            text: z.string().min(1).max(500)
          })
        }
      }
    }
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: todoResponseSchema
        }
      },
      description: "Todo created"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Validation error"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Forbidden"
    }
  }
});

todosRouter.openapi(createTodoRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId } = c.req.param();
  const { text } = c.req.valid("json");

  // Check if user has access to this organization
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId
    }
  });

  if (!membership) {
    return c.json(
      {
        error: "Forbidden - you don't have access to this organization",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  const todo = await prisma.todo.create({
    data: {
      text,
      userId: user.id,
      organizationId
    }
  });

  return c.json(
    {
      id: todo.id,
      text: todo.text,
      completed: todo.completed,
      createdAt: todo.createdAt.toISOString(),
      updatedAt: todo.updatedAt.toISOString()
    },
    201
  );
});

// Update todo
const updateTodoRoute = createRoute({
  method: "patch",
  path: "/organizations/{organizationId}/todos/{id}",
  tags: ["Todos"],
  summary: "Update todo",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int()
    }),
    body: {
      content: {
        "application/json": {
          schema: z.object({
            text: z.string().min(1).max(500).optional(),
            completed: z.boolean().optional()
          })
        }
      }
    }
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: todoResponseSchema
        }
      },
      description: "Todo updated"
    },
    400: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Validation error"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Forbidden"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Todo not found"
    }
  }
});

todosRouter.openapi(updateTodoRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();
  const updateData = c.req.valid("json");

  // Check if user has access to this organization
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId
    }
  });

  if (!membership) {
    return c.json(
      {
        error: "Forbidden - you don't have access to this organization",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  // Check if todo exists and belongs to this organization
  const existingTodo = await prisma.todo.findFirst({
    where: {
      id: Number(id),
      organizationId
    }
  });

  if (!existingTodo) {
    return notFound("Todo not found");
  }

  // Only allow users to update their own todos or if they're admin/owner
  if (existingTodo.userId !== user.id && !["admin", "owner"].includes(membership.role)) {
    return c.json(
      {
        error: "Forbidden - you can only update your own todos",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  const todo = await prisma.todo.update({
    where: {
      id: Number(id)
    },
    data: updateData
  });

  return c.json(
    {
      id: todo.id,
      text: todo.text,
      completed: todo.completed,
      createdAt: todo.createdAt.toISOString(),
      updatedAt: todo.updatedAt.toISOString()
    },
    200
  );
});

// Delete todo
const deleteTodoRoute = createRoute({
  method: "delete",
  path: "/organizations/{organizationId}/todos/{id}",
  tags: ["Todos"],
  summary: "Delete todo",
  request: {
    params: z.object({
      organizationId: z.string(),
      id: z.coerce.number().int()
    })
  },
  responses: {
    204: {
      description: "Todo deleted"
    },
    401: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Unauthorized"
    },
    403: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Forbidden"
    },
    404: {
      content: {
        "application/json": {
          schema: schemas.error
        }
      },
      description: "Todo not found"
    }
  }
});

todosRouter.openapi(deleteTodoRoute, async (c) => {
  const user = c.get("user");
  if (!user) {
    return unauthorized("Authentication required");
  }

  const { organizationId, id } = c.req.param();

  // Check if user has access to this organization
  const membership = await prisma.member.findFirst({
    where: {
      userId: user.id,
      organizationId
    }
  });

  if (!membership) {
    return c.json(
      {
        error: "Forbidden - you don't have access to this organization",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  // Check if todo exists and belongs to this organization
  const existingTodo = await prisma.todo.findFirst({
    where: {
      id: Number(id),
      organizationId
    }
  });

  if (!existingTodo) {
    return notFound("Todo not found");
  }

  // Only allow users to delete their own todos or if they're admin/owner
  if (existingTodo.userId !== user.id && !["admin", "owner"].includes(membership.role)) {
    return c.json(
      {
        error: "Forbidden - you can only delete your own todos",
        timestamp: new Date().toISOString()
      },
      403
    );
  }

  await prisma.todo.delete({
    where: {
      id: Number(id)
    }
  });

  return c.body(null, 204);
});