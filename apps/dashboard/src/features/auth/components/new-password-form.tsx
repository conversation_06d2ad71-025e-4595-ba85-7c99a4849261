/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { authClient } from "@repo/authentication/auth-client";
import { Button } from "@repo/ui/components/button";
import { Input } from "@repo/ui/components/input";
import { Label } from "@repo/ui/components/label";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const newPasswordSchema = z
  .object({
    password: z.string().min(8, "Password must be at least 8 characters"),
    confirmPassword: z.string()
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
  });

type FormData = z.infer<typeof newPasswordSchema>;

const NewPasswordForm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm<FormData>();

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    setLoading(true);
    setError(""); // Clear any previous errors

    if (!token) {
      setError("Invalid reset token. Please request a new password reset.");
      setLoading(false);
      return;
    }

    try {
      await authClient.resetPassword({
        newPassword: data.password,
        token
      });
      setIsSubmitted(true);
      router.push("/sign-in");
    } catch (err) {
      setError("An error occurred. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Show error if no token
  if (!token) {
    return (
      <div>
        <div className="flex flex-col space-y-2 p-6">
          <h1 className="text-3xl pb-4">Invalid Reset Link</h1>
          <p className="">
            This password reset link is invalid or has expired. Please request a new password reset.
          </p>
        </div>
        <div className="items-center p-6 pt-0 text-muted-foreground flex justify-start gap-1">
          <p>
            <Link
              className="hover:underline"
              href="/reset-password"
            >
              Request new password reset
            </Link>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex flex-col space-y-2 p-6">
        <h1 className="text-3xl pb-4">Reset your password</h1>
        <p className="">
          Enter your new password and confirm it to reset your password.
        </p>
      </div>
      <div className="p-6 pt-0 flex flex-col gap-4">
        <form
          className="flex flex-col gap-4"
          onSubmit={handleSubmit(onSubmit)}
        >
          {/* TODO: Add alert component */}
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 rounded-md">
              {error}
            </div>
          )}
          <div className="space-y-2 flex w-full flex-col">
            <Label htmlFor="password">New password</Label>
            <Input
              id="password"
              type="password"
              placeholder="********"
              {...register("password")}
              disabled={loading}
            />
            {errors.password && (
              <p className="text-sm text-destructive">
                {errors.password.message}
              </p>
            )}
          </div>
          <div className="space-y-2 flex w-full flex-col">
            <Label htmlFor="confirm-password">Confirm password</Label>
            <Input
              id="confirm-password"
              type="password"
              placeholder="********"
              {...register("confirmPassword")}
              disabled={loading}
            />
            {errors.confirmPassword && (
              <p className="text-sm text-destructive">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
          <Button
            type="submit"
            disabled={isSubmitting || loading}
            loading={isSubmitting}
          >
            Reset Password
          </Button>
        </form>
      </div>
      <div className="items-center p-6 pt-0 text-muted-foreground flex justify-start gap-1">
        <p>
          <Link
            className="hover:underline"
            href="/sign-in"
          >
            Back to sign in
          </Link>
        </p>
      </div>
    </div>
  );
};

export default NewPasswordForm;
