name: Production Dashboard Deployment

on:
  push:
    branches: [main]
    paths:
      - "apps/dashboard/**"
      - "packages/**"
      - "turbo.json"
      - "package.json"
      - "pnpm-lock.yaml"

concurrency:
  group: production-dashboard-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy:
    name: Deploy to Vercel
    runs-on: ubuntu-latest
    environment:
      name: production
      url: https://app.centaly.com
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 9.15.4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "pnpm"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Generate Prisma Client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Install Vercel CLI
        run: pnpm add -g vercel

      - name: Deploy to Vercel
        run: vercel --prod --yes --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
          BETTER_AUTH_URL: ${{ secrets.BETTER_AUTH_URL }}
          RESEND_API_KEY: ${{ secrets.RESEND_API_KEY }}
          EMAIL_FROM: ${{ secrets.EMAIL_FROM }}
          NEXT_PUBLIC_API_URL: ${{ secrets.NEXT_PUBLIC_API_URL }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}

      - name: Comment deployment status
        uses: actions/github-script@v7
        if: github.event_name == 'push'
        with:
          script: |
            console.log(`🚀 Production dashboard deployed to: https://app.centaly.com`);
