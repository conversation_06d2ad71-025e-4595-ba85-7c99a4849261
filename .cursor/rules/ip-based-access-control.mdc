---
description:
globs:
alwaysApply: false
---

# IP-Based Access Control

## Overview

This document explains the IP-based access control system implemented for the Centaly dashboard application. The system restricts access to staging and production environments based on client IP addresses, redirecting unauthorized users to the marketing website.

## How It Works

The system uses a Next.js middleware that intercepts all incoming requests and validates the client's IP address against a whitelist. If the IP is not whitelisted, users are automatically redirected to the marketing site.

### Request Flow

1. **User accesses app** → `staging.centaly.com` or `app.centaly.com`
2. **Middleware executes** → Runs on Vercel's Edge Runtime
3. **IP detection** → Extracts client IP from request headers
4. **Whitelist check** → Compares IP against `ALLOWED_IPS` environment variable
5. **Decision**:
   - ✅ **Whitelisted IP** → Allow access to dashboard
   - ❌ **Non-whitelisted IP** → Redirect to `https://www.centaly.com`
   - ⚠️ **Error/Missing config** → Allow access (fail-safe)

## Implementation Details

### File Location

```
apps/dashboard/src/middleware.ts
```

### Environment Variables

| Variable        | Description                                      | Example                       |
| --------------- | ------------------------------------------------ | ----------------------------- |
| `ALLOWED_IPS`   | Comma-separated list of whitelisted IP addresses | `***************,***********` |
| `MARKETING_URL` | Redirect destination for unauthorized users      | `https://www.centaly.com`     |

### IP Detection Strategy

The middleware uses multiple fallback methods to detect the client's IP address:

1. **Primary**: `x-forwarded-for` header (first IP in comma-separated list)
2. **Fallback 1**: `x-real-ip` header
3. **Fallback 2**: `x-vercel-forwarded-for` header

This approach ensures compatibility with various proxy configurations and CDN setups.

### Matcher Configuration

The middleware only runs on specific paths to optimize performance:

**Included**: All routes except:

- API routes (`/api/*`)
- Static files (`/_next/static/*`)
- Image optimization (`/_next/image/*`)
- Favicon (`/favicon.ico`)
- Public assets (`/public/*`)

## Configuration Management

### Adding New IP Addresses

1. **Go to Vercel Dashboard** → Project → Settings → Environment Variables
2. **Edit `ALLOWED_IPS`** variable
3. **Add new IP** to comma-separated list: `***************,NEW_IP_HERE`
4. **Save changes** → Takes effect immediately (no redeploy needed)

### Changing Marketing URL

1. **Edit `MARKETING_URL`** environment variable in Vercel
2. **Update value** to new destination URL
3. **Save changes**

### Environment Setup

Ensure both environment variables are configured in:

- ✅ **Preview (Staging)** environment
- ✅ **Production** environment

## Enable/Disable Access Control

### Enable

- Middleware file exists at `apps/dashboard/src/middleware.ts`
- Environment variables are properly configured
- System is active automatically

### Disable

- **Option 1**: Delete `apps/dashboard/src/middleware.ts` and redeploy
- **Option 2**: Set `ALLOWED_IPS` to `*` (allows all IPs)
- **Option 3**: Remove environment variables (fail-safe allows access)

## Troubleshooting

### Common Issues

**Problem**: "User can't access the app from whitelisted IP"

- **Check**: IP address format in `ALLOWED_IPS`
- **Verify**: User's actual IP address (may change with VPN/proxy)
- **Solution**: Add correct IP to whitelist

**Problem**: "Everyone can access the app (no restrictions)"

- **Check**: Environment variables are set correctly
- **Verify**: Middleware file exists and is deployed
- **Check**: Vercel Function logs for middleware execution

**Problem**: "App is completely inaccessible"

- **Emergency fix**: Remove `ALLOWED_IPS` environment variable
- **Or**: Delete middleware file and redeploy
- **Then**: Investigate IP detection issues

### Debugging

1. **Check Vercel Function Logs**:
   - Go to Vercel Dashboard → Project → Functions tab
   - Look for middleware execution logs
   - Check for "Middleware error:" messages

2. **Test IP Detection**:
   - Use online tools to verify your current IP
   - Compare with `ALLOWED_IPS` configuration
   - Test from different networks/VPNs

## Security Considerations

### IP Spoofing Protection

- Relies on Vercel's trusted proxy headers
- Headers are validated by Vercel's infrastructure
- Direct IP spoofing at the edge level is not possible

### Fail-Safe Design

- **Missing environment variables** → Allow access
- **Middleware errors** → Allow access
- **Invalid configuration** → Allow access

This prevents accidental lockouts while maintaining security when properly configured.

### Environment Variable Security

- Variables are stored securely in Vercel dashboard
- Not exposed in client-side code
- Access controlled by Vercel team permissions

## Development Workflow

### Local Development

- Middleware doesn't run in local development (`next dev`)
- Use Vercel Preview deployments to test IP restrictions
- Environment variables from `.env.local` are not used by middleware

### Testing Changes

1. **Deploy to staging** → Test with your IP
2. **Test with VPN** → Verify redirects work
3. **Deploy to production** → Monitor logs for issues

### Code Changes

- Middleware uses TypeScript for type safety
- Changes require redeployment to take effect
- No external dependencies or build steps required

## Emergency Procedures

### Team Lockout Recovery

1. **Immediate**: Remove `ALLOWED_IPS` environment variable in Vercel
2. **Or**: Delete `apps/dashboard/src/middleware.ts` and push to main branch
3. **Then**: Add current team IPs to whitelist
4. **Finally**: Re-enable access control

### Incident Response

- **Monitor**: Vercel Function logs for errors
- **Alert**: High redirect rates may indicate IP changes
- **Response**: Update IP whitelist as needed

## Future Enhancements

### Potential Improvements

- **CIDR notation support** → Allow IP ranges (e.g., `***********/24`)
- **Geographic restrictions** → Allow/block by country
- **Time-based access** → Restrict access during specific hours
- **User-based whitelisting** → Allow specific authenticated users

## Related Documentation

- [Next.js Middleware Documentation](https://nextjs.org/docs/app/building-your-application/routing/middleware)
- [Vercel Edge Runtime](https://vercel.com/docs/functions/edge-functions/edge-runtime)
- [Environment Variables in Vercel](https://vercel.com/docs/projects/environment-variables)
