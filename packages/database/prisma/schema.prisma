// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

enum Role {
  member      @map("member")
  admin       @map("admin")
  owner       @map("owner")
}

enum OnboardingStatus {
  incomplete @map("incomplete")
  workspace  @map("workspace")
  invite     @map("invite") 
  complete   @map("complete")
}

enum InvitationStatus {
  pending  @map("pending")
  accepted @map("accepted")
  rejected @map("rejected")
  canceled @map("canceled")
  }

model User {
  id               String           @id
  name             String
  email            String
  emailVerified    Boolean
  image            String?
  createdAt        DateTime
  updatedAt        DateTime
  onboardingStatus OnboardingStatus @default(incomplete)
  defaultWorkspace String?          // Slug of the user's default workspace
  role             String           @default("user") // Better-Auth admin plugin
  banned           Boolean          @default(false)  // Better-Auth admin plugin
  banReason        String?                          // Better-Auth admin plugin
  banExpires       DateTime?                        // Better-Auth admin plugin
  sessions         Session[]
  accounts         Account[]

  members     Member[]
  invitations Invitation[]
  todos       Todo[]

  @@unique([email])
  @@index([onboardingStatus])  // Index for onboarding queries
  @@index([defaultWorkspace])  // Index for workspace lookups
  @@index([banned])            // Index for banned users
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  activeOrganizationId String?
  impersonatedBy      String?  // Better-Auth admin plugin

  @@index([userId])
  @@index([activeOrganizationId])  // Index for active org queries
  @@index([token, userId])         // Composite index for auth queries
  @@index([expiresAt])             // Index for session cleanup
  @@index([impersonatedBy])        // Index for impersonation queries
  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@index([userId])
  @@index([accountId, providerId])  // Composite index for provider lookups
  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
  @@index([identifier])         // Index for email/identifier lookups
  @@index([expiresAt])         // Index for cleanup
}

model Organization {
  id          String       @id
  name        String
  slug        String?
  logo        String?
  createdAt   DateTime
  metadata    String?
  members     Member[]
  invitations Invitation[]
  todos       Todo[]

  @@unique([slug])
  @@index([createdAt])    // Index for sorting by creation date
  @@index([name])         // Index for name-based searches
  @@map("organization")
}

model Member {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           Role         
  
  createdAt      DateTime
  @@index([userId, organizationId])  // Composite index for membership queries
  @@index([organizationId, role])    // Index for role-based queries
  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           Role?       
  status         InvitationStatus
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())

  @@index([organizationId])
  @@index([inviterId])
  @@index([email])               // Index for email-based lookups
  @@index([status])              // Index for status filtering
  @@index([expiresAt])           // Index for cleanup
  @@map("invitation")
}

model Todo {
  id             Int          @id @default(autoincrement())
  text           String
  completed      Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([organizationId])
  @@index([userId, organizationId])
  @@map("todos")
}