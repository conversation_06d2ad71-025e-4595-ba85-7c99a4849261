import { apiPatch } from "@/src/lib/config/api";

export async function updateOnboardingStatus(
  status: "incomplete" | "workspace" | "invite" | "complete"
) {
  const response = await apiPatch("/api/v1/profile/onboarding-status", {
    onboardingStatus: status
  });

  if (!response.ok) {
    throw new Error(`Failed to update onboarding status: ${response.status}`);
  }

  return response.json();
}

export async function updateDefaultWorkspace(workspaceSlug: string | null) {
  const response = await apiPatch("/api/v1/profile/default-workspace", {
    defaultWorkspace: workspaceSlug
  });

  if (!response.ok) {
    throw new Error(`Failed to update default workspace: ${response.status}`);
  }

  return response.json();
}
